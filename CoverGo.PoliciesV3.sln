
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35707.178
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{089100B1-113F-4E66-888A-E83F3999EAFD}"
	ProjectSection(SolutionItems) = preProject
		helm\Chart.yaml = helm\Chart.yaml
		.github\CODEOWNERS = .github\CODEOWNERS
		Directory.Build.props = Directory.Build.props
		Directory.Build.targets = Directory.Build.targets
		Directory.Packages.props = Directory.Packages.props
		docker-compose.ci.yml = docker-compose.ci.yml
		docker-compose.yml = docker-compose.yml
		Dockerfile = Dockerfile
		helm\values.yaml = helm\values.yaml
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.PoliciesV3.Api", "src\CoverGo.PoliciesV3.Api\CoverGo.PoliciesV3.Api.csproj", "{FF730C22-7BC7-4DF5-A145-3861A92A2B06}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.PoliciesV3.Application", "src\CoverGo.PoliciesV3.Application\CoverGo.PoliciesV3.Application.csproj", "{CA38326E-29AB-4DB4-A799-C7022B7C8FE2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.PoliciesV3.Domain", "src\CoverGo.PoliciesV3.Domain\CoverGo.PoliciesV3.Domain.csproj", "{AC05FEFC-1B83-4822-9B7B-53425062A460}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.PoliciesV3.Infrastructure", "src\CoverGo.PoliciesV3.Infrastructure\CoverGo.PoliciesV3.Infrastructure.csproj", "{4D000495-AA46-4D61-9C24-F27832E89B42}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.PoliciesV3.Tests.Unit", "tests\CoverGo.PoliciesV3.Tests.Unit\CoverGo.PoliciesV3.Tests.Unit.csproj", "{519D03C3-B529-45F4-A4F0-88D689989FE7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.PoliciesV3.Tests.Integration", "tests\CoverGo.PoliciesV3.Tests.Integration\CoverGo.PoliciesV3.Tests.Integration.csproj", "{8C61F693-445E-4F23-A502-8A1501991D00}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FF730C22-7BC7-4DF5-A145-3861A92A2B06}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF730C22-7BC7-4DF5-A145-3861A92A2B06}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF730C22-7BC7-4DF5-A145-3861A92A2B06}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF730C22-7BC7-4DF5-A145-3861A92A2B06}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA38326E-29AB-4DB4-A799-C7022B7C8FE2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA38326E-29AB-4DB4-A799-C7022B7C8FE2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA38326E-29AB-4DB4-A799-C7022B7C8FE2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA38326E-29AB-4DB4-A799-C7022B7C8FE2}.Release|Any CPU.Build.0 = Release|Any CPU
		{AC05FEFC-1B83-4822-9B7B-53425062A460}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC05FEFC-1B83-4822-9B7B-53425062A460}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC05FEFC-1B83-4822-9B7B-53425062A460}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AC05FEFC-1B83-4822-9B7B-53425062A460}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D000495-AA46-4D61-9C24-F27832E89B42}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D000495-AA46-4D61-9C24-F27832E89B42}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D000495-AA46-4D61-9C24-F27832E89B42}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D000495-AA46-4D61-9C24-F27832E89B42}.Release|Any CPU.Build.0 = Release|Any CPU
		{519D03C3-B529-45F4-A4F0-88D689989FE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{519D03C3-B529-45F4-A4F0-88D689989FE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{519D03C3-B529-45F4-A4F0-88D689989FE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{519D03C3-B529-45F4-A4F0-88D689989FE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{8C61F693-445E-4F23-A502-8A1501991D00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8C61F693-445E-4F23-A502-8A1501991D00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8C61F693-445E-4F23-A502-8A1501991D00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8C61F693-445E-4F23-A502-8A1501991D00}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
