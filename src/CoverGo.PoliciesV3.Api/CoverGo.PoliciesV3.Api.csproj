<Project Sdk="Microsoft.NET.Sdk.Web">

    <ItemGroup>
        <PackageReference Include="CoverGo.BuildingBlocks.Api.GraphQl" />
        <PackageReference Include="CoverGo.BuildingBlocks.Auth" />
        <PackageReference Include="CoverGo.BuildingBlocks.Bootstrapper" />
        <PackageReference Include="CoverGo.BuildingBlocks.Observability" />
        <PackageReference Include="CoverGo.BuildingBlocks.Scheduler.Hangfire" />
        <PackageReference Include="Grpc.AspNetCore" />
        <PackageReference Include="HotChocolate.AspNetCore" />
        <PackageReference Include="HotChocolate.AspNetCore.Authorization" />
        <PackageReference Include="HotChocolate.Types.Analyzers">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="HotChocolate.Types.Scalars" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.FeatureManagement.AspNetCore" />
        <PackageReference Include="Microsoft.SourceLink.GitHub">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Swashbuckle.AspNetCore" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\CoverGo.PoliciesV3.Application\CoverGo.PoliciesV3.Application.csproj" />
      <ProjectReference Include="..\CoverGo.PoliciesV3.Domain\CoverGo.PoliciesV3.Domain.csproj" />
      <ProjectReference Include="..\CoverGo.PoliciesV3.Infrastructure\CoverGo.PoliciesV3.Infrastructure.csproj" />
    </ItemGroup>

</Project>
