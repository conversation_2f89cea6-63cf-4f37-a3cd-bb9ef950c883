using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Application.Common.Constants;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads;

/// <summary>
/// API for exporting detailed validation error information
/// </summary>
[Authorize]
[ApiController]
[Route("api/v1/policies/{policyId}/members/uploads")]
[Tags("Policy Member Upload Errors")]
public class ErrorDetailsController : ControllerBase
{
    private readonly IPolicyMemberUploadValidationErrorExporterService _exporter;
    private readonly IPermissionValidator _permissionValidator;

    public ErrorDetailsController(
        IPolicyMemberUploadValidationErrorExporterService exporter,
        IPermissionValidator permissionValidator)
    {
        _exporter = exporter ?? throw new ArgumentNullException(nameof(exporter));
        _permissionValidator = permissionValidator ?? throw new ArgumentNullException(nameof(permissionValidator));
    }

    /// <summary>
    /// Export detailed validation errors from a policy member upload
    /// </summary>
    /// <param name="policyId">The policy ID</param>
    /// <param name="policyMemberUploadId">The policy member upload ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>CSV file containing detailed validation error information</returns>
    /// <response code="200">Returns the CSV file with detailed validation errors</response>
    /// <response code="400">If there was an error processing the request</response>
    /// <response code="401">If the user is not authenticated</response>
    /// <response code="403">If the user doesn't have permission to access this policy</response>
    [HttpGet("{policyMemberUploadId}/error_details")]
    [ProducesResponseType(typeof(FileResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Produces("text/csv")]
    public async Task<IActionResult> ErrorDetails(
        [FromRoute] string policyId,
        [FromRoute] string policyMemberUploadId,
        CancellationToken cancellationToken)
    {
        try
        {
            await _permissionValidator.AuthorizeAsync(
                User.Identities.First(),
                new PermissionRequest(PermissionConstants.Policies.Update, PermissionConstants.Policies.Write).WithTargetIds(policyId));

            byte[] result = await _exporter.ExportValidationErrors(policyMemberUploadId, cancellationToken);
            return File(result, "text/csv", $"policies_error_details_{policyId}_members_uploads_{policyMemberUploadId}.csv");
        }
        catch (Exception exception)
        {
            return BadRequest(exception.Message);
        }
    }
}