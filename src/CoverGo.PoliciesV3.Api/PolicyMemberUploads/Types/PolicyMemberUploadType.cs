using PolicyMemberUploadEntity = CoverGo.PoliciesV3.Domain.PolicyMemberUploads.PolicyMemberUpload;

namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.Types;

/// <summary>
/// GraphQL type for PolicyMemberUpload with explicit field control
/// Only exposes the fields we want in the GraphQL API
/// </summary>
public class PolicyMemberUploadType : ObjectType<PolicyMemberUploadEntity>
{
    protected override void Configure(IObjectTypeDescriptor<PolicyMemberUploadEntity> descriptor)
    {
        descriptor.Name("policies_PolicyMemberUpload");

        // Ignore domain infrastructure fields
        descriptor.Ignore(x => x.DomainEvents);

        descriptor.Ignore(x => x.EntityAuditInfo);

        // Ignore domain methods that shouldn't be exposed in GraphQL
        descriptor.Ignore(x => x.CanCompleteValidation());

        descriptor.Ignore(x => x.GetImportedMembersCount());

        descriptor.Ignore(x => x.GetImportedMemberIds());

        descriptor.Ignore(x => x.RequiresMemberCleanup());

        // Ignore technical fields
        descriptor.Ignore(x => x.RowVersion);

        // Explicitly define the fields we want to expose
        descriptor.Field(x => x.Id)
            .Type<NonNullType<IdType>>();

        descriptor.Field(x => x.PolicyId)
            .Type<NonNullType<IdType>>();

        descriptor.Field(x => x.EndorsementId)
            .Type<IdType>();

        descriptor.Field(x => x.Path)
            .Type<NonNullType<StringType>>();

        descriptor.Field(x => x.MembersCount)
            .Type<NonNullType<IntType>>();

        descriptor.Field(x => x.ValidMembersCount)
            .Type<IntType>();

        descriptor.Field(x => x.InvalidMembersCount)
            .Type<IntType>();

        descriptor.Field(x => x.Status)
            .Type<NonNullType<EnumType<PolicyMemberUploadStatus>>>();

        descriptor.Field(x => x.EntityAuditInfo.CreatedAt)
            .Name("createdAt")
            .Type<NonNullType<DateTimeType>>();

        descriptor.Field(x => x.EntityAuditInfo.LastModifiedAt)
            .Name("lastModifiedAt")
            .Type<DateTimeType>();

        descriptor.Field(x => x.ValidationErrors)
            .Type<NonNullType<ListType<NonNullType<PolicyMemberUploadValidationErrorType>>>>();

        descriptor.Field(x => x.ImportedResults)
            .Type<NonNullType<ListType<NonNullType<PolicyMemberUploadImportedResultType>>>>();

        // Add a computed field for completion status if needed by clients
        descriptor.Field("completed")
            .Type<BooleanType>()
            .Resolve(context =>
            {
                PolicyMemberUploadEntity upload = context.Parent<PolicyMemberUploadEntity>();
                return upload.Status.Value == Domain.PolicyMemberUploads.PolicyMemberUploadStatus.IMPORTED.Value ||
                       upload.Status.Value == Domain.PolicyMemberUploads.PolicyMemberUploadStatus.CANCELED.Value ||
                       upload.Status.Value == Domain.PolicyMemberUploads.PolicyMemberUploadStatus.FAILED.Value;
            });
    }
}
