namespace CoverGo.PoliciesV3.Api.PolicyMemberUploads.Types;

/// <summary>
/// Simple GraphQL enum for PolicyMemberUploadStatus
/// This mirrors the domain value object but as a proper GraphQL enum
/// </summary>
public enum PolicyMemberUploadStatus
{
    REGISTERED,
    VA<PERSON><PERSON><PERSON><PERSON>,
    VA<PERSON><PERSON><PERSON>ING_ERROR,
    VA<PERSON><PERSON><PERSON><PERSON>,
    IMPORTI<PERSON>,
    IMPORTING_ERROR,
    IMPORTED,
    REVERT<PERSON>,
    REVERTED,
    CANCELING,
    CANCELED,
    FAILED
}
