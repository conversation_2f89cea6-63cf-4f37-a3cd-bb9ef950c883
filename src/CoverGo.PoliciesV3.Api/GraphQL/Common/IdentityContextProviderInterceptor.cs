using HotChocolate.AspNetCore;
using HotChocolate.Execution;
using System.Security.Claims;

namespace CoverGo.PoliciesV3.Api.GraphQL.Common;

public class IdentityContextProviderInterceptor : DefaultHttpRequestInterceptor
{
    public override ValueTask OnCreateAsync(
        HttpContext context,
        IRequestExecutor requestExecutor,
        IQueryRequestBuilder requestBuilder,
        CancellationToken cancellationToken)
    {
        // Set ClaimsIdentity in global state if authenticated
        if (context.User?.Identity is ClaimsIdentity identity && identity.IsAuthenticated)
            requestBuilder.SetGlobalState("identity", identity);
        return base.OnCreateAsync(context, requestExecutor, requestBuilder, cancellationToken);
    }
}