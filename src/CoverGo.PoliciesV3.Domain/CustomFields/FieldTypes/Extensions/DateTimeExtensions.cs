using System.Globalization;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;

/// <summary>
/// Extension methods for DateTime parsing and validation
/// </summary>
public static class DateTimeExtensions
{
    private static readonly string[] ValidFormats = [
        "yyyy-MM-dd",
        "yyyy-MM-ddTHH:mm:ssK",
        "dd/MM/yyyy"
    ];

    /// <summary>
    /// Tries to parse a string as DateTime using predefined formats
    /// </summary>
    /// <param name="value">The string value to parse</param>
    /// <returns>True if parsing succeeds, false otherwise</returns>
    public static bool TryParseDateTime(this string value) => !string.IsNullOrWhiteSpace(value) && (DateTime.TryParse(value, CultureInfo.InvariantCulture, DateTimeStyles.None, out _) ||
               DateTime.TryParseExact(value, ValidFormats, CultureInfo.InvariantCulture, DateTimeStyles.None, out _));

    /// <summary>
    /// Parses a string as DateTime using predefined formats
    /// </summary>
    /// <param name="value">The string value to parse</param>
    /// <returns>The parsed DateTime</returns>
    /// <exception cref="FormatException">Thrown when the string cannot be parsed</exception>
    public static DateTime ParseDateTime(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new FormatException("Cannot parse null or empty string as DateTime");

        if (DateTime.TryParse(value, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
            return result;

        if (DateTime.TryParseExact(value, ValidFormats, CultureInfo.InvariantCulture, DateTimeStyles.None, out result))
            return result;

        throw new FormatException($"Unable to parse '{value}' as DateTime using supported formats");
    }
}
