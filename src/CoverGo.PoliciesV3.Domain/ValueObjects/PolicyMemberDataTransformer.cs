using System.Text.Json;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

public static class PolicyMemberDataTransformer
{
    public static MembersUploadFields TransformFileLabelsToInternalFieldNames(
        IEnumerable<IReadOnlyDictionary<string, string?>> rawMemberDataFromFile,
        PolicyMemberFieldsSchema policyFieldsSchema)
    {
        PrecomputedSchemaLookups precomputedLookups = BuildSchemaLookupCaches(policyFieldsSchema);

        // Use parallel processing for large datasets while maintaining order
        var memberDataList = rawMemberDataFromFile.ToList();

        // Only use parallel processing for larger datasets
        List<MemberUploadFields> transformedMembers;

        if (memberDataList.Count >= ValidationConstants.BatchProcessing.ParallelThreshold)
        {
            // Parallel processing with order preservation
            transformedMembers = memberDataList.AsParallel()
                .AsOrdered()
                .Select(rawMemberData => TransformSingleMemberData(precomputedLookups, rawMemberData))
                .ToList();
        }
        else
        {
            // Sequential processing for smaller datasets to avoid parallel overhead
            transformedMembers = new List<MemberUploadFields>(memberDataList.Count);
            foreach (IReadOnlyDictionary<string, string?> rawMemberData in memberDataList)
            {
                transformedMembers.Add(TransformSingleMemberData(precomputedLookups, rawMemberData));
            }
        }

        return transformedMembers.Count == 0 ? MembersUploadFields.Empty() : new(transformedMembers);
    }

    private sealed record PrecomputedSchemaLookups(
        IReadOnlyDictionary<string, PolicyMemberFieldDefinition> FieldDefinitionsByLabel,
        IReadOnlyDictionary<string, string> ObjectFieldLabelToInternalNameMap,
        IReadOnlyDictionary<string, string> InnerFieldLabelToInternalNameMap,
        IReadOnlyDictionary<string, IReadOnlyList<StringOption>> StringOptionsByInternalFieldName,
        IReadOnlyDictionary<string, IReadOnlyList<NumberOption>> NumberOptionsByInternalFieldName);

    private static PrecomputedSchemaLookups BuildSchemaLookupCaches(PolicyMemberFieldsSchema policyFieldsSchema)
    {
        var fieldDefinitionsByLabel = policyFieldsSchema.Fields.ToDictionary(field => field.Label, field => field);

        var objectFieldLabelToInternalNameMap = policyFieldsSchema.Fields
            .Where(field => field.Type is ObjectFieldType)
            .DistinctBy(field => field.Label)
            .ToDictionary(field => field.Label, field => field.Name);

        var innerFieldLabelToInternalNameMap = policyFieldsSchema.Fields
            .Select(field => field.Type)
            .OfType<ObjectFieldType>()
            .SelectMany(objectType => objectType.InnerFieldDefinitions)
            .DistinctBy(innerField => innerField.Label)
            .ToDictionary(innerField => innerField.Label, innerField => innerField.Name);

        var stringOptionsByInternalFieldName = policyFieldsSchema.Fields
            .Where(field => field.Type is IOptionsFieldType<StringOption> stringOptionsType && stringOptionsType.Options != null)
            .ToDictionary(field => field.Name, field => (IReadOnlyList<StringOption>)((IOptionsFieldType<StringOption>)field.Type).Options!);

        var numberOptionsByInternalFieldName = policyFieldsSchema.Fields
            .Where(field => field.Type is IOptionsFieldType<NumberOption> numberOptionsType && numberOptionsType.Options != null)
            .ToDictionary(field => field.Name, field => (IReadOnlyList<NumberOption>)((IOptionsFieldType<NumberOption>)field.Type).Options!);

        return new PrecomputedSchemaLookups(
            fieldDefinitionsByLabel,
            objectFieldLabelToInternalNameMap,
            innerFieldLabelToInternalNameMap,
            stringOptionsByInternalFieldName,
            numberOptionsByInternalFieldName);
    }

    private static MemberUploadFields TransformSingleMemberData(
        PrecomputedSchemaLookups precomputedLookups,
        IReadOnlyDictionary<string, string?> rawMemberData)
    {
        var transformedMemberFields = new Dictionary<string, string?>();

        ProcessSimpleFieldsWithOptions(precomputedLookups, rawMemberData, transformedMemberFields);
        ProcessComplexObjectFields(precomputedLookups, rawMemberData, transformedMemberFields);

        return new(transformedMemberFields);
    }

    private static void ProcessSimpleFieldsWithOptions(
        PrecomputedSchemaLookups precomputedLookups,
        IReadOnlyDictionary<string, string?> rawMemberData,
        Dictionary<string, string?> transformedMemberFields)
    {
        foreach ((string fileLabel, PolicyMemberFieldDefinition fieldDefinition) in precomputedLookups.FieldDefinitionsByLabel)
        {
            // Skip object fields as they are handled separately
            if (fieldDefinition.Type is ObjectFieldType)
                continue;

            string? rawValue = rawMemberData.TryGetValueOrDefault(fileLabel);
            if (string.IsNullOrEmpty(rawValue))
                continue;

            // Determine field value based on field type using pre-computed lookups
            string? transformedValue = rawValue;

            if (precomputedLookups.StringOptionsByInternalFieldName.TryGetValue(fieldDefinition.Name, out IReadOnlyList<StringOption>? stringOptions))
            {
                // For string options, try to find a matching option by value
                transformedValue = stringOptions.FirstOrDefault(option =>
                    string.Equals(option.Value, rawValue, StringComparison.OrdinalIgnoreCase))?.Value ?? rawValue;
            }
            else if (precomputedLookups.NumberOptionsByInternalFieldName.TryGetValue(fieldDefinition.Name, out IReadOnlyList<NumberOption>? numberOptions))
            {
                // For number options, try to find a matching option by numeric value
                transformedValue = numberOptions.FirstOrDefault(option =>
                    string.Equals(option.Value.ToString(), rawValue, StringComparison.OrdinalIgnoreCase))?.Value.ToString() ?? rawValue;
            }

            transformedMemberFields[fieldDefinition.Name] = transformedValue;
        }
    }

    private static void ProcessComplexObjectFields(
        PrecomputedSchemaLookups precomputedLookups,
        IReadOnlyDictionary<string, string?> rawMemberDataWithHeaders,
        Dictionary<string, string?> transformedMemberFields)
    {
        const string ObjectFieldHeaderSeparator = " - ";
        const int ExpectedObjectFieldHeaderParts = 2;

        if (precomputedLookups.ObjectFieldLabelToInternalNameMap.Count == 0) return;

        // This dictionary will collect the data for each object field before serialization.
        // Key: Internal name of the main object field (e.g., "mailingAddress")
        // Value: A dictionary of inner field internal names to their values (e.g., {"street1": "123 Main St", "postalCode": "90210"})
        var objectFieldDataCollector = precomputedLookups.ObjectFieldLabelToInternalNameMap
            .ToDictionary(kvp => kvp.Value, _ => new Dictionary<string, string?>());

        foreach ((string rawFileHeader, string? rawFieldValue) in rawMemberDataWithHeaders)
        {
            // Expect headers for object inner fields to follow a pattern like "Mailing Address - Street Line 1"
            var headerParts = rawFileHeader.Split([ObjectFieldHeaderSeparator], StringSplitOptions.None)
                                          .Select(part => part.Trim())
                                          .ToList();

            if (headerParts.Count != ExpectedObjectFieldHeaderParts) continue;

            string objectFieldFileLabel = headerParts[0];
            string innerFieldFileLabel = headerParts[1];

            // Try to find the internal names for both the main object field and the inner field using pre-computed lookups
            if (precomputedLookups.ObjectFieldLabelToInternalNameMap.TryGetValue(objectFieldFileLabel, out string? objectInternalName) &&
                precomputedLookups.InnerFieldLabelToInternalNameMap.TryGetValue(innerFieldFileLabel, out string? innerInternalName))
            {
                // If both names are found, store the value in our collector
                objectFieldDataCollector[objectInternalName][innerInternalName] = rawFieldValue;
            }
        }

        // Serialize the collected data for each object field and add it to the final transformed fields.
        foreach ((string objectInternalName, Dictionary<string, string?> innerFieldsData) in objectFieldDataCollector)
        {
            if (innerFieldsData.Count == 0) continue; // Skip if no inner fields were actually collected for this object

            // If all collected values for an object's inner fields are null or empty,
            // treat the entire object field as null. Otherwise, serialize to JSON.
            string? serializedObjectFieldValue = innerFieldsData.All(kvp => string.IsNullOrEmpty(kvp.Value))
                ? null
                : JsonSerializer.Serialize(innerFieldsData);

            transformedMemberFields.Add(objectInternalName, serializedObjectFieldValue);
        }
    }
}
