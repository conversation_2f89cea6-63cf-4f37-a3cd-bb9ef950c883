namespace CoverGo.PoliciesV3.Domain.Common.Constants;

/// <summary>
/// Constants for validation operations to avoid magic strings and numbers.
/// Centralizes all validation-related configuration values and string literals.
/// </summary>
public static class ValidationConstants
{
    /// <summary>
    /// Common labels used in validation error messages and UI display
    /// </summary>
    public static class Labels
    {
        /// <summary>
        /// Standard label for upload file fields in validation errors
        /// </summary>
        public const string UploadFile = "Upload File";

        /// <summary>
        /// Standard key for error messages in validation contexts
        /// </summary>
        public const string ErrorMessage = "ErrorMessage";

        /// <summary>
        /// Standard label for columns in validation errors
        /// </summary>
        public const string Columns = "Columns";

        /// <summary>
        /// Standard label for fields in validation errors
        /// </summary>
        public const string Fields = "Fields";

        /// <summary>
        /// Standard label for operation in validation errors
        /// </summary>
        public const string Operation = "Operation";

        /// <summary>
        /// Standard label for policy-related validation errors
        /// </summary>
        public const string Policy = "Policy";

        /// <summary>
        /// Standard label for upload-related validation errors
        /// </summary>
        public const string Upload = "Upload";

        /// <summary>
        /// Standard label for primary member-related validation errors
        /// </summary>
        public const string PrimaryMember = "Primary Member";

        /// <summary>
        /// Standard label for member ID-related validation errors
        /// </summary>
        public const string MemberId = "Member ID";

        /// <summary>
        /// Standard label for email-related validation errors
        /// </summary>
        public const string Email = "Email";

        /// <summary>
        /// Standard label for passport number-related validation errors
        /// </summary>
        public const string PassportNo = "Passport No";

        /// <summary>
        /// Standard label for HKID-related validation errors
        /// </summary>
        public const string Hkid = "HKID";

        /// <summary>
        /// Standard label for staff number-related validation errors
        /// </summary>
        public const string StaffNo = "Staff No";
    }

    /// <summary>
    /// Common property paths used in validation errors
    /// </summary>
    public static class PropertyPaths
    {
        /// <summary>
        /// Property path for file-related validation errors
        /// </summary>
        public const string File = "file";

        /// <summary>
        /// Property path for columns-related validation errors
        /// </summary>
        public const string Columns = "columns";

        /// <summary>
        /// Property path for fields-related validation errors
        /// </summary>
        public const string Fields = "fields";

        /// <summary>
        /// Property path for operation-related validation errors
        /// </summary>
        public const string Operation = "operation";

        /// <summary>
        /// Property path for policy-related validation errors
        /// </summary>
        public const string Policy = "policy";

        /// <summary>
        /// Property path for member ID-related validation errors
        /// </summary>
        public const string MemberId = "memberId";

        /// <summary>
        /// Property path for email-related validation errors
        /// </summary>
        public const string Email = "email";

        /// <summary>
        /// Property path for passport number-related validation errors
        /// </summary>
        public const string PassportNo = "passportNo";

        /// <summary>
        /// Property path for HKID-related validation errors
        /// </summary>
        public const string Hkid = "hkid";

        /// <summary>
        /// Property path for staff number-related validation errors
        /// </summary>
        public const string StaffNo = "staffNo";
    }

    /// <summary>
    /// Context keys used in validation error contexts for providing additional information
    /// </summary>
    public static class ContextKeys
    {
        /// <summary>
        /// Context key for pattern in validation errors (e.g., regex patterns)
        /// </summary>
        public const string Pattern = "Pattern";

        /// <summary>
        /// Context key for minimum value in range validation errors
        /// </summary>
        public const string MinValue = "MinValue";

        /// <summary>
        /// Context key for maximum value in range validation errors
        /// </summary>
        public const string MaxValue = "MaxValue";

        /// <summary>
        /// Context key for row index in file processing validation errors
        /// </summary>
        public const string RowIndex = "RowIndex";

        /// <summary>
        /// Context key for member ID in validation errors
        /// </summary>
        public const string MemberId = "MemberId";

        /// <summary>
        /// Context key for policy ID in validation errors
        /// </summary>
        public const string PolicyId = "PolicyId";

        /// <summary>
        /// Context key for upload ID in validation errors
        /// </summary>
        public const string UploadId = "UploadId";

        /// <summary>
        /// Context key for value in validation errors
        /// </summary>
        public const string Value = "Value";

        /// <summary>
        /// Context key for message in validation errors
        /// </summary>
        public const string Message = "Message";

        /// <summary>
        /// Context key for original rule in validation errors
        /// </summary>
        public const string OriginalRule = "OriginalRule";

        /// <summary>
        /// Context key for scope in validation errors (e.g., uniqueness scope)
        /// </summary>
        public const string Scope = "Scope";

        /// <summary>
        /// Context key for available options in validation errors
        /// </summary>
        public const string AvailableOptions = "AvailableOptions";

        /// <summary>
        /// Context key for minimum length in string validation errors
        /// </summary>
        public const string MinLength = "MinLength";

        /// <summary>
        /// Context key for maximum length in string validation errors
        /// </summary>
        public const string MaxLength = "MaxLength";

        /// <summary>
        /// Context key for minimum age in age validation errors
        /// </summary>
        public const string MinAge = "MinAge";

        /// <summary>
        /// Context key for maximum age in age validation errors
        /// </summary>
        public const string MaxAge = "MaxAge";

        /// <summary>
        /// Context key for actual age in age validation errors
        /// </summary>
        public const string ActualAge = "ActualAge";

        /// <summary>
        /// Context key for minimum days in date validation errors
        /// </summary>
        public const string MinDays = "MinDays";

        /// <summary>
        /// Context key for actual days in date validation errors
        /// </summary>
        public const string ActualDays = "ActualDays";

        /// <summary>
        /// Context key for existing policy member ID in uniqueness validation errors
        /// </summary>
        public const string ExistingPolicyMemberId = "ExistingPolicyMemberId";

        /// <summary>
        /// Context key for plan ID in validation errors
        /// </summary>
        public const string PlanId = "PlanId";

        /// <summary>
        /// Context key for available plans in validation errors
        /// </summary>
        public const string AvailablePlans = "AvailablePlans";

        /// <summary>
        /// Context key for product ID in validation errors
        /// </summary>
        public const string ProductId = "ProductId";

        /// <summary>
        /// Context key for required fields in validation errors
        /// </summary>
        public const string RequiredFields = "RequiredFields";

        /// <summary>
        /// Context key for field errors in validation errors
        /// </summary>
        public const string FieldErrors = "FieldErrors";

        /// <summary>
        /// Context key for expected columns in file validation errors
        /// </summary>
        public const string ExpectedColumns = "ExpectedColumns";

        /// <summary>
        /// Context key for actual columns in file validation errors
        /// </summary>
        public const string ActualColumns = "ActualColumns";

        /// <summary>
        /// Context key for missing columns in file validation errors
        /// </summary>
        public const string MissingColumns = "MissingColumns";

        /// <summary>
        /// Context key for extra columns in file validation errors
        /// </summary>
        public const string ExtraColumns = "ExtraColumns";

        /// <summary>
        /// Context key for file type in validation errors
        /// </summary>
        public const string FileType = "FileType";
    }

    /// <summary>
    /// Default values used in validation when no specific value is provided
    /// </summary>
    public static class DefaultValues
    {
        /// <summary>
        /// Default scope for uniqueness validation (typically "upload")
        /// </summary>
        public const string DefaultScope = "upload";
    }

    /// <summary>
    /// Scope values used in uniqueness validation to define the boundary of uniqueness checks
    /// </summary>
    public static class Scopes
    {
        /// <summary>
        /// Upload scope for uniqueness validation within a single upload file
        /// </summary>
        public const string Upload = "upload";

        /// <summary>
        /// Policy scope for uniqueness validation within a single policy
        /// </summary>
        public const string Policy = "policy";

        /// <summary>
        /// Tenant scope for uniqueness validation within a tenant
        /// </summary>
        public const string Tenant = "tenant";

        /// <summary>
        /// Contract holder scope for uniqueness validation within contract holder policies
        /// </summary>
        public const string ContractHolder = "contract holder";
    }

    /// <summary>
    /// Regex patterns and delimiters used in validation rules
    /// </summary>
    public static class RegexPatterns
    {
        /// <summary>
        /// Prefix for regex patterns in validation rules (e.g., "regex:pattern")
        /// </summary>
        public const string RegexPrefix = "regex:";

        /// <summary>
        /// Delimiter character for patterns in validation rules
        /// </summary>
        public const char PatternDelimiter = '/';
    }

    /// <summary>
    /// Exception messages used in validation operations
    /// </summary>
    public static class ExceptionMessages
    {
        /// <summary>
        /// Message for when minimum value is greater than maximum value in range validation
        /// </summary>
        public const string MinValueGreaterThanMaxValue = "Minimum value cannot be greater than maximum value.";
    }

    /// <summary>
    /// Timeout values for validation operations to prevent performance issues
    /// </summary>
    public static class Timeouts
    {
        /// <summary>
        /// Timeout in milliseconds for regex pattern matching operations.
        /// Prevents denial of service attacks from catastrophic backtracking.
        /// </summary>
        public const int RegexTimeoutMilliseconds = 3000;
    }

    /// <summary>
    /// Concurrency settings for validation operations to optimize performance
    /// </summary>
    public static class Concurrency
    {
        /// <summary>
        /// Default maximum number of concurrent validations if not configured
        /// </summary>
        public const int DefaultMaxConcurrentValidations = 20;
    }

    /// <summary>
    /// Batch processing settings for validation operations to optimize memory usage
    /// </summary>
    public static class BatchProcessing
    {
        /// <summary>
        /// Default batch size for processing member data in chunks to optimize memory usage.
        /// Used for member identifier extraction and validation operations.
        /// </summary>
        public const int DefaultMemberBatchSize = 1000;

        /// <summary>
        /// Default batch size for database insertion operations to optimize performance.
        /// Used for bulk policy member creation and other database batch operations.
        /// </summary>
        public const int DefaultDatabaseInsertBatchSize = 250;

        /// <summary>
        /// Maximum number of batches that can be processed concurrently.
        /// Limits parallel processing to prevent resource exhaustion.
        /// </summary>
        public const int MaxConcurrentBatches = 3;

        public const int ParallelThreshold = 100;
    }

    /// <summary>
    /// File size limits for upload validation to prevent performance issues
    /// </summary>
    public static class FileSizeLimits
    {
        /// <summary>
        /// Maximum allowed size for CSV files in bytes (50MB)
        /// </summary>
        public const long MaxCsvFileSizeBytes = 50L * 1024 * 1024;

        /// <summary>
        /// Maximum allowed size for XLSX files in bytes (100MB)
        /// </summary>
        public const long MaxXlsxFileSizeBytes = 100L * 1024 * 1024;

        /// <summary>
        /// Maximum allowed size for any file type in bytes (100MB)
        /// </summary>
        public const long MaxGeneralFileSizeBytes = 100L * 1024 * 1024;

        /// <summary>
        /// Minimum file size in bytes to be considered valid (1 byte)
        /// </summary>
        public const long MinFileSizeBytes = 1L;
    }

    /// <summary>
    /// Exception data keys for contextual information in exceptions
    /// </summary>
    public static class ExceptionDataKeys
    {
        /// <summary>
        /// Key for storing upload information in exception data
        /// </summary>
        public const string Upload = "Upload";
    }
}
