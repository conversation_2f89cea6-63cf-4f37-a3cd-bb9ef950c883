using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;

/// <summary>
/// Business Rules:
/// - HKID, passport numbers, and staff numbers must be unique within the upload file for new members
/// - Empty or null identification values are not validated for uniqueness
/// - Only new members (without memberIds) are validated for identification uniqueness
/// - Existing members (with memberIds) are excluded from upload uniqueness validation
/// - Validates hkid, passportNo, and staffNo fields
/// </summary>
public class UploadMustHaveUniqueIdentificationSpecification(
    ILogger<UploadMustHaveUniqueIdentificationSpecification> logger)
    : ISpecification<UploadUniquenessValidationContext>
{
    private static readonly HashSet<string> IdentificationFields = new(StringComparer.OrdinalIgnoreCase)
            { "hkid", "passportNo", "staffNo" };

    public string BusinessRuleName => "Upload Must Have Unique Identification";
    public string Description => "Validates that identification fields (HKID, passport, staff number) are unique within the upload file to prevent duplicate identification entries for new members";

    public async Task<Result> IsSatisfiedBy(UploadUniquenessValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            await Task.CompletedTask;

            Dictionary<string, Dictionary<string, List<int>>> fieldIndexes = BuildIdentificationIndexes(context.MembersFields);
            Dictionary<int, List<ValidationError>> memberErrors = ValidateIdentificationUniqueness(fieldIndexes, context.Schema);

            if (memberErrors.Count > 0)
            {
                var allErrors = memberErrors.Values.Where(errors => errors != null).SelectMany(errors => errors).ToList();
                logger.LogWarning("Upload identification uniqueness validation failed. Found {ErrorCount} duplicate identification errors across {MemberCount} members",
                    allErrors.Count, memberErrors.Count);
                return Result.Failure(allErrors);
            }

            logger.LogDebug("Upload identification uniqueness validation passed - no duplicate identification fields found");
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during upload identification uniqueness validation");
            throw;
        }
    }

    private static Dictionary<string, Dictionary<string, List<int>>> BuildIdentificationIndexes(MembersUploadFields membersFields)
    {
        var fieldIndexes = new Dictionary<string, Dictionary<string, List<int>>>(StringComparer.OrdinalIgnoreCase);

        for (int i = 0; i < membersFields.Count; i++)
        {
            MemberUploadFields memberFields = membersFields[i];

            // Only validate new members (without memberIds) for upload uniqueness
            string? memberId = memberFields.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.MemberIdField);
            if (!string.IsNullOrWhiteSpace(memberId))
                continue;

            foreach (string fieldName in IdentificationFields)
            {
                string? fieldValue = memberFields.Value.TryGetValueOrDefault(fieldName);
                if (string.IsNullOrWhiteSpace(fieldValue))
                    continue;

                if (!fieldIndexes.TryGetValue(fieldName, out Dictionary<string, List<int>>? fieldIndex))
                {
                    fieldIndex = new Dictionary<string, List<int>>(StringComparer.OrdinalIgnoreCase);
                    fieldIndexes[fieldName] = fieldIndex;
                }

                if (!fieldIndex.TryGetValue(fieldValue, out List<int>? indexList))
                {
                    indexList = [];
                    fieldIndex[fieldValue] = indexList;
                }
                indexList.Add(i);
            }
        }

        return fieldIndexes;
    }

    private static Dictionary<int, List<ValidationError>> ValidateIdentificationUniqueness(
        Dictionary<string, Dictionary<string, List<int>>> fieldIndexes,
        PolicyMemberFieldsSchema schema)
    {
        var validationErrors = new Dictionary<int, List<ValidationError>>();

        foreach (string fieldName in IdentificationFields)
        {
            if (!fieldIndexes.TryGetValue(fieldName, out Dictionary<string, List<int>>? fieldIndex))
                continue;

            PolicyMemberFieldDefinition? fieldDefinition = TryGetFieldDefinition(schema, fieldName);
            if (fieldDefinition == null)
                continue;

            ProcessDuplicateValuesForField(fieldIndex, fieldDefinition, validationErrors);
        }

        return validationErrors;
    }

    private static PolicyMemberFieldDefinition? TryGetFieldDefinition(PolicyMemberFieldsSchema schema, string fieldName)
    {
        return schema.Fields.FirstOrDefault(f =>
            string.Equals(f.Name, fieldName, StringComparison.OrdinalIgnoreCase));
    }

    private static void ProcessDuplicateValuesForField(
        Dictionary<string, List<int>> fieldIndex,
        PolicyMemberFieldDefinition fieldDefinition,
        Dictionary<int, List<ValidationError>> validationErrors)
    {
        foreach (List<int> duplicateIndexes in fieldIndex.Values.Where(indexes => indexes.Count > 1))
        {
            foreach (int memberIndex in duplicateIndexes)
            {
                ValidationError error = Errors.UniqueViolation(fieldDefinition.Name, fieldDefinition.GetFullLabel(), ValidationConstants.Scopes.Upload);
                AddValidationError(validationErrors, memberIndex, error);
            }
        }
    }

    private static void AddValidationError(Dictionary<int, List<ValidationError>> validationErrors, int memberIndex, ValidationError error)
    {
        if (!validationErrors.TryGetValue(memberIndex, out List<ValidationError>? errors))
        {
            errors = [];
            validationErrors[memberIndex] = errors;
        }
        errors.Add(error);
    }

    /// <summary>
    /// Validates identification uniqueness and returns row-indexed errors for integration with validation orchestration.
    /// </summary>
    public virtual Dictionary<int, List<ValidationError>> ValidateWithRowIndexedErrors(UploadUniquenessValidationContext context)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            Dictionary<string, Dictionary<string, List<int>>> fieldIndexes = BuildIdentificationIndexes(context.MembersFields);
            return ValidateIdentificationUniqueness(fieldIndexes, context.Schema);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during upload identification uniqueness validation with row-indexed errors");
            throw;
        }
    }
}