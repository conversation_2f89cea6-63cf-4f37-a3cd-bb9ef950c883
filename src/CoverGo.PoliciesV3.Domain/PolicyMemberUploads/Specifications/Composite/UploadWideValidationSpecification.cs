using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;

public class UploadWideValidationSpecification(
    UploadMustHaveUniqueEmailsSpecification uploadUniqueEmailsSpec,
    UploadMustHaveUniqueIdentificationSpecification uploadUniqueIdSpec,
    UploadMustHaveUniqueMemberIdsSpecification uploadUniqueMemberIdsSpec,
    DependentAndEmployeeMustBeOnSamePlanSpecification dependentPlanSpec,
    IUploadValidationOrchestrator validationOrchestrator,
    ILogger<UploadWideValidationSpecification> logger) : CompositeSpecificationBase<UploadWideValidationContext>
{
    #region Specification Properties

    public override string BusinessRuleName => "Upload-Wide Validation";
    public override string Description => "Validates business rules that apply across the entire upload dataset";

    #endregion

    #region Main Validation Logic

    public override async Task<BatchValidationResult> EvaluateBusinessRulesAsync(
        UploadWideValidationContext context,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            logger.LogInformation("Starting upload-wide validation for {MemberCount} members", context.MemberCount);

            if (!context.HasMembersToValidate)
            {
                logger.LogInformation("No members to validate - returning success");
                return BatchValidationResult.Success(0);
            }

            Dictionary<int, List<ValidationError>> allErrors = await validationOrchestrator.ExecuteUploadValidationsAsync(
                context, CreateValidationSpecs(), cancellationToken);

            int validCount = context.MemberCount - allErrors.Count;
            int invalidCount = allErrors.Count;

            logger.LogInformation("Upload-wide validation completed. Valid: {ValidCount}, Invalid: {InvalidCount}",
                validCount, invalidCount);

            return new BatchValidationResult
            {
                ValidCount = validCount,
                InvalidCount = invalidCount,
                RowErrors = allErrors
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in upload-wide validation");
            throw;
        }
    }

    #endregion

    #region Protected Virtual Methods (For Testing)

    protected virtual UploadValidationSpecs CreateValidationSpecs() => new()
    {
        UniqueEmailsSpec = uploadUniqueEmailsSpec,
        UniqueIdSpec = uploadUniqueIdSpec,
        UniqueMemberIdsSpec = uploadUniqueMemberIdsSpec,
        DependentPlanSpec = dependentPlanSpec
    };

    #endregion
}

#region Supporting Interfaces and Classes

public interface IUploadValidationOrchestrator
{
    Task<Dictionary<int, List<ValidationError>>> ExecuteUploadValidationsAsync(
        UploadWideValidationContext context,
        UploadValidationSpecs specs,
        CancellationToken cancellationToken);
}

public sealed class UploadValidationSpecs
{
    public required UploadMustHaveUniqueEmailsSpecification UniqueEmailsSpec { get; init; }
    public required UploadMustHaveUniqueIdentificationSpecification UniqueIdSpec { get; init; }
    public required UploadMustHaveUniqueMemberIdsSpecification UniqueMemberIdsSpec { get; init; }
    public required DependentAndEmployeeMustBeOnSamePlanSpecification DependentPlanSpec { get; init; }
}

public class UploadValidationOrchestrator(ILogger<UploadValidationOrchestrator> logger) : IUploadValidationOrchestrator
{
    public async Task<Dictionary<int, List<ValidationError>>> ExecuteUploadValidationsAsync(
        UploadWideValidationContext context,
        UploadValidationSpecs specs,
        CancellationToken cancellationToken)
    {
        Dictionary<int, List<ValidationError>> allErrors = [];

        // 1. UPLOAD UNIQUENESS VALIDATIONS
        await ExecuteUploadUniquenessValidations(context, specs, allErrors, cancellationToken);

        // 2. DEPENDENT PLAN VALIDATION
        await ExecuteDependentPlanValidation(context, specs, allErrors, cancellationToken);

        return allErrors;
    }

    private async Task ExecuteUploadUniquenessValidations(
        UploadWideValidationContext context,
        UploadValidationSpecs specs,
        Dictionary<int, List<ValidationError>> allErrors,
        CancellationToken cancellationToken)
    {
        var uploadContext = UploadUniquenessValidationContext.Create(context.MembersFields, context.Schema);

        (string, Dictionary<int, List<ValidationError>>)[] uniquenessValidations =
        [
            ("Email", specs.UniqueEmailsSpec.ValidateWithRowIndexedErrors(uploadContext)),
            ("ID", specs.UniqueIdSpec.ValidateWithRowIndexedErrors(uploadContext)),
            ("MemberID", specs.UniqueMemberIdsSpec.ValidateWithRowIndexedErrors(uploadContext))
        ];

        foreach ((string validationType, Dictionary<int, List<ValidationError>> errors) in uniquenessValidations)
        {
            MergeErrorsIntoCollection(allErrors, errors);
            logger.LogDebug("{ValidationType} uniqueness validation completed. Errors: {ErrorCount}",
                validationType, errors.Count);
        }

        await Task.CompletedTask;
    }

    private async Task ExecuteDependentPlanValidation(
        UploadWideValidationContext context,
        UploadValidationSpecs specs,
        Dictionary<int, List<ValidationError>> allErrors,
        CancellationToken cancellationToken)
    {
        ResolvedValidationData resolvedData = context.ResolvedData;
        bool shouldApply = resolvedData.UseTheSamePlanForEmployeeAndDependents;
        bool shouldApplyOnlyForSme = resolvedData.OnlyApplyForSmeProducts;
        bool isProductSme = resolvedData.IsProductSme;

        logger.LogDebug("Dependent plan validation flags: shouldApply={ShouldApply}, shouldApplyOnlyForSme={ShouldApplyOnlyForSme}, isProductSme={IsProductSme}",
            shouldApply, shouldApplyOnlyForSme, isProductSme);

        var dependentContext = DependentPlanValidationContext.Create(
            context.MembersFields,
            context.Schema,
            shouldApply,
            shouldApplyOnlyForSme,
            isProductSme);

        Dictionary<int, List<ValidationError>> dependentErrors = specs.DependentPlanSpec.ValidateWithRowIndexedErrors(dependentContext);
        MergeErrorsIntoCollection(allErrors, dependentErrors);

        logger.LogDebug("Dependent plan validation completed. Errors: {DependentErrors}", dependentErrors.Count);

        await Task.CompletedTask;
    }

    private static void MergeErrorsIntoCollection(
        Dictionary<int, List<ValidationError>> targetErrors,
        Dictionary<int, List<ValidationError>> sourceErrors)
    {
        foreach (KeyValuePair<int, List<ValidationError>> kvp in sourceErrors)
        {
            if (!targetErrors.TryGetValue(kvp.Key, out List<ValidationError>? existingErrors))
            {
                targetErrors[kvp.Key] = [.. kvp.Value];
            }
            else
            {
                existingErrors.AddRange(kvp.Value);
            }
        }
    }
}

#endregion
