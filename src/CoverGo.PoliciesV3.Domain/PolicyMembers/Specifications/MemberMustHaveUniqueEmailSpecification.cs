using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

/// <summary>
/// Email validation scopes:
/// - Tenant: Email must be unique across all members in the tenant (if field marked as unique)
/// - Policy: Email must be unique within the policy scope
/// - Empty or null email values are not validated for uniqueness
/// </summary>
public class MemberMustHaveUniqueEmailSpecification(
    IPolicyMemberUniquenessService policyMemberUniquenessService,
    ILogger<MemberMustHaveUniqueEmailSpecification> logger)
    : ISpecification<UniquenessValidationContext>
{
    public string BusinessRuleName => "Member Must Have Unique Email";
    public string Description => "Validates that member email addresses are unique within tenant scope (if marked as unique) and policy scope - DDD compliant with domain objects";

    public virtual async Task<Result> IsSatisfiedBy(UniquenessValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            List<ValidationError> errors = await ValidateEmailInAllScopes(context, cancellationToken);

            return errors.Count == 0
                ? Result.Success()
                : Result.Failure(errors);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during email uniqueness validation for policy: {PolicyId}", context.PolicyId);
            throw;
        }
    }

    private async Task<List<ValidationError>> ValidateEmailInAllScopes(
        UniquenessValidationContext context,
        CancellationToken cancellationToken)
    {
        var errors = new List<ValidationError>();

        var tenantUniqueFieldSet = context.Schema.Fields
            .Where(f => f.IsUnique && f.Name.Equals("email", StringComparison.OrdinalIgnoreCase))
            .Select(f => f.Name)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        var policyUniqueFieldSet = context.Schema.Fields
            .Where(f => f.Name.Equals("email", StringComparison.OrdinalIgnoreCase))
            .Select(f => f.Name)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        IReadOnlyDictionary<string, object> effectiveFieldValues = context.GetEffectiveFieldValues();

        var tenantRules = tenantUniqueFieldSet
            .Select(field => new FieldValidationRule(
                Field: field,
                Scope: ValidationScope.Tenant,
                Type: ValidationType.Unique,
                Value: effectiveFieldValues.TryGetValue(field, out object? value) ? value : null
            ))
            .ToList();

        var policyRules = policyUniqueFieldSet
            .Select(field => new FieldValidationRule(
                Field: field,
                Scope: ValidationScope.Policy,
                Type: ValidationType.Unique,
                Value: effectiveFieldValues.TryGetValue(field, out object? value) ? value : null
            ))
            .ToList();

        IEnumerable<ValidationError> tenantErrors = await ValidateTenantScope(context, tenantRules, cancellationToken);
        IEnumerable<ValidationError> policyErrors = await ValidatePolicyScope(context, policyRules, cancellationToken);

        errors.AddRange(tenantErrors);
        errors.AddRange(policyErrors);

        if (errors.Count > 0)
        {
            logger.LogWarning("Member uniqueness validation failed for PolicyId: {PolicyId}, Errors: {@Errors}",
                context.PolicyId, errors.Select(x => x.Message));
        }

        return errors;
    }

    private async Task<IEnumerable<ValidationError>> ValidateTenantScope(
        UniquenessValidationContext context,
        List<FieldValidationRule> rules,
        CancellationToken cancellationToken)
    {
        var fieldValues = rules
            .Select(rule => new
            {
                Name = rule.Field,
                Value = context.GetEffectiveFieldValues().TryGetValue(rule.Field, out object? fieldValue) ? fieldValue : null
            })
            .Where(field => field.Value != null && field.Value.ToString() != "")
            .ToList();

        if (fieldValues.Count == 0)
        {
            return [];
        }

        var validEndorsementIds = context.Policy.Endorsements
            .Where(e => !EndorsementStatus.DoNotAccountFor.Contains(e.Status))
            .Select(e => e.Id)
            .ToList<string?>();

        validEndorsementIds.Add(null);

        var fieldNames = fieldValues.Select(f => f.Name).ToList();
        var fieldValuesDict = fieldValues.ToDictionary(f => f.Name, f => f.Value!);
        var endorsementIds = validEndorsementIds
            .Where(id => !string.IsNullOrEmpty(id) && Guid.TryParse(id, out _))
            .Select(id => (EndorsementId)id!)
            .ToList();

        List<string> duplicateFields = await policyMemberUniquenessService.ValidateTenantScopeUniquenessAsync(
            (PolicyId)context.PolicyId,
            context.GetEffectiveMemberId(),
            context.GetEffectivePolicyMemberId() != null ? (PolicyMemberId)context.GetEffectivePolicyMemberId()! : null,
            fieldValuesDict,
            fieldNames,
            endorsementIds,
            cancellationToken);

        var errors = (duplicateFields ?? []).Select(fieldName =>
        {
            PolicyMemberFieldDefinition? fieldDefinition = context.Schema.GetField(fieldName);
            return Errors.UniqueViolation(fieldName, fieldDefinition?.Label, ValidationConstants.Scopes.Tenant);
        }).ToList();

        if (errors.Count > 0)
        {
            logger.LogWarning("Found {ErrorCount} tenant scope validation errors for policy {PolicyId}",
                errors.Count, context.PolicyId);
        }

        return errors;
    }

    private async Task<IEnumerable<ValidationError>> ValidatePolicyScope(
        UniquenessValidationContext context,
        List<FieldValidationRule> rules,
        CancellationToken cancellationToken)
    {
        var fieldValues = rules
            .Select(rule => new
            {
                Name = rule.Field,
                Value = context.GetEffectiveFieldValues().TryGetValue(rule.Field, out object? fieldValue) ? fieldValue : null
            })
            .Where(field => field.Value != null && field.Value.ToString() != "")
            .ToList();

        if (fieldValues.Count == 0)
        {
            return [];
        }

        var validEndorsementIds = context.Policy.Endorsements
            .Where(e => !EndorsementStatus.DoNotAccountFor.Contains(e.Status))
            .Select(e => e.Id)
            .Cast<string?>()
            .Append(null)
            .ToList();

        var fieldNames = fieldValues.Select(f => f.Name).ToList();
        var fieldValuesDict = fieldValues.ToDictionary(f => f.Name, f => f.Value!);
        var endorsementIds = validEndorsementIds
            .Where(id => !string.IsNullOrEmpty(id) && Guid.TryParse(id, out _))
            .Select(id => (EndorsementId)id!)
            .ToList();

        List<string> duplicateFields = await policyMemberUniquenessService.ValidatePolicyScopeUniquenessAsync(
            (PolicyId)context.PolicyId,
            context.GetEffectiveMemberId(),
            context.GetEffectivePolicyMemberId() != null ? (PolicyMemberId)context.GetEffectivePolicyMemberId()! : null,
            fieldValuesDict,
            fieldNames,
            endorsementIds,
            cancellationToken);

        var errors = (duplicateFields ?? []).Select(fieldName =>
        {
            PolicyMemberFieldDefinition? fieldDefinition = context.Schema.GetField(fieldName);
            return Errors.UniqueViolation(fieldName, fieldDefinition?.Label, ValidationConstants.Scopes.Policy);
        }).ToList();

        if (errors.Count > 0)
        {
            logger.LogWarning("Found {ErrorCount} policy scope validation errors for policy {PolicyId}",
                errors.Count, context.PolicyId);
        }

        return errors;
    }
}
