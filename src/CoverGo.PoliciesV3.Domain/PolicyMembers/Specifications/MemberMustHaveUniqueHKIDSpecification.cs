using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

/// <summary>
/// - HKID must be unique within policy scope (within single policy) for policy holder fields
/// - HKID must be unique within contract holder scope (across all policies for a contract holder) when contract holder policies are provided
/// - Empty or null HKID values are not validated for uniqueness
/// - Only considers members in valid endorsement states (excludes canceled/rejected endorsements)
/// - Contract holder scope validation can be disabled via feature flag "SkipContractHolderUniqueRulesWhenAddPolicyMember"
/// </summary>
public class MemberMustHaveUniqueHKIDSpecification(
    IPolicyMemberUniquenessService policyMemberUniquenessService,
    ILogger<MemberMustHaveUniqueHKIDSpecification> logger)
    : ISpecification<UniquenessValidationContext>
{
    public string BusinessRuleName => "Member Must Have Unique HKID";
    public string Description => "Validates that member HKID (Hong Kong Identity Document) numbers are unique within the appropriate scope (policy or contract holder) based on business rules";

    public virtual async Task<Result> IsSatisfiedBy(UniquenessValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            string? hkidNumber = ExtractHKID(context);
            if (hkidNumber == null)
            {
                logger.LogDebug("No HKID value provided for member validation, skipping HKID uniqueness check");
                return Result.Success();
            }

            logger.LogDebug("Validating HKID uniqueness for HKID: {HKID} in policy: {PolicyId}", hkidNumber, context.PolicyId);

            PolicyMemberFieldDefinition? hkidFieldDefinition = context.GetFieldDefinition("hkid");
            if (hkidFieldDefinition == null)
            {
                logger.LogDebug("HKID field not found in schema, skipping HKID uniqueness validation");
                return Result.Success();
            }

            var validationErrors = new List<ValidationError>();

            List<ValidationError> policyScopeErrors = await ValidatePolicyScopeUniqueness(context, hkidNumber, hkidFieldDefinition, cancellationToken);
            validationErrors.AddRange(policyScopeErrors);

            if (ShouldValidateContractHolderScope(context))
            {
                List<ValidationError> contractHolderScopeErrors = await ValidateContractHolderScopeUniqueness(context, hkidNumber, hkidFieldDefinition, cancellationToken);
                validationErrors.AddRange(contractHolderScopeErrors);
            }

            if (validationErrors.Count > 0)
            {
                logger.LogWarning("HKID uniqueness validation failed for HKID: {HKID} in policy: {PolicyId}. Errors: {ErrorCount}",
                    hkidNumber, context.PolicyId, validationErrors.Count);
                return Result.Failure(validationErrors);
            }

            logger.LogDebug("HKID uniqueness validation passed for HKID: {HKID} in policy: {PolicyId}", hkidNumber, context.PolicyId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during HKID uniqueness validation for policy: {PolicyId}", context.PolicyId);
            throw;
        }
    }

    private static string? ExtractHKID(UniquenessValidationContext context)
    {
        string? hkidValue = context.MemberFields.Value.TryGetValue("hkid", out string? value) ? value : null;
        return string.IsNullOrWhiteSpace(hkidValue) ? null : hkidValue;
    }

    private bool ShouldValidateContractHolderScope(UniquenessValidationContext context)
    {
        if (context.ContractHolderPolicyIds.Count == 0 || string.IsNullOrWhiteSpace(context.ContractHolderId))
            return false;

        if (context.ShouldSkipContractHolderValidation)
        {
            logger.LogDebug("Skipping contract holder scope HKID validation due to business rule");
            return false;
        }

        return true;
    }

    private async Task<List<ValidationError>> ValidatePolicyScopeUniqueness(
        UniquenessValidationContext context,
        string hkidNumber,
        PolicyMemberFieldDefinition hkidFieldDefinition,
        CancellationToken cancellationToken)
    {
        try
        {
            List<string?> validEndorsementIds = GetValidEndorsementIds(context.Policy);
            var endorsementIdCollection = EndorsementIdCollection.FromStringIds(validEndorsementIds);

            var fieldValues = new Dictionary<string, object> { ["hkid"] = hkidNumber };
            var fieldNames = new List<string> { "hkid" };

            PolicyId policyIdForHkid = context.PolicyId;

            List<string> duplicateFields = await policyMemberUniquenessService.ValidatePolicyScopeUniquenessAsync(
                policyIdForHkid,
                null, // MemberId is null for new member creation
                null, // PolicyMemberId is null for new member creation
                fieldValues,
                fieldNames,
                endorsementIdCollection,
                cancellationToken);

            var errors = (duplicateFields ?? [])
                .Where(fieldName => fieldName == "hkid")
                .Select(_ => Errors.UniqueViolation(ValidationConstants.PropertyPaths.Hkid, hkidFieldDefinition.Label, ValidationConstants.Scopes.Policy))
                .ToList();

            if (errors.Count > 0)
            {
                logger.LogWarning("Policy scope HKID uniqueness validation failed for HKID: {HKID} in policy: {PolicyId}",
                    hkidNumber, context.PolicyId);
            }

            return errors;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during policy scope HKID uniqueness validation for policy: {PolicyId}", context.PolicyId);
            throw;
        }
    }

    private async Task<List<ValidationError>> ValidateContractHolderScopeUniqueness(
        UniquenessValidationContext context,
        string hkidNumber,
        PolicyMemberFieldDefinition hkidFieldDefinition,
        CancellationToken cancellationToken)
    {
        try
        {
            var endorsementIdCollection = EndorsementIdCollection.FromEndorsementIds(
                context.ContractHolderScopeEndorsements ?? []);

            var contractHolderPolicyGuids = (context.ContractHolderPolicyIds ?? [])
                .Where(id => Guid.TryParse(id, out _))
                .Select(id => (PolicyId)id)
                .ToList();

            if (contractHolderPolicyGuids.Count == 0)
            {
                logger.LogDebug("No valid contract holder policy IDs provided, skipping contract holder scope HKID validation");
                return [];
            }

            var fieldValues = new Dictionary<string, object> { ["hkid"] = hkidNumber };
            var fieldNames = new List<string> { "hkid" };

            List<string> duplicateFields = await policyMemberUniquenessService.ValidateContractHolderScopeUniquenessAsync(
                null, // MemberId is null for new member creation
                null, // PolicyMemberId is null for new member creation
                contractHolderPolicyGuids,
                endorsementIdCollection,
                fieldValues,
                fieldNames,
                cancellationToken);

            var errors = (duplicateFields ?? [])
                .Where(fieldName => fieldName == "hkid")
                .Select(_ => Errors.UniqueViolation(ValidationConstants.PropertyPaths.Hkid, hkidFieldDefinition.Label, ValidationConstants.Scopes.ContractHolder))
                .ToList();

            if (errors.Count > 0)
            {
                logger.LogWarning("Contract holder scope HKID uniqueness validation failed for HKID: {HKID} in contract holder: {ContractHolderId}",
                    hkidNumber, context.ContractHolderId);
            }

            return errors;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during contract holder scope HKID uniqueness validation for contract holder: {ContractHolderId}", context.ContractHolderId);
            throw;
        }
    }

    private static List<string?> GetValidEndorsementIds(PolicyDto policy)
    {
        var validEndorsementIds = (policy.Endorsements ?? [])
            .Where(e => !EndorsementStatus.DoNotAccountFor.Contains(e.Status))
            .Select(e => e.Id)
            .ToList<string?>();

        validEndorsementIds.Add(null); // Always include null for base policy state
        return validEndorsementIds;
    }
}
