<Project Sdk="Microsoft.NET.Sdk">
  <ItemGroup>
    <PackageReference Include="CoverGo.BuildingBlocks.Application.Core" />
    <PackageReference Include="CoverGo.BuildingBlocks.Auth" />
    <PackageReference Include="CoverGo.BuildingBlocks.DataAccess" />
    <PackageReference Include="CoverGo.BuildingBlocks.DataAccess.PostgreSql" />
    <PackageReference Include="CoverGo.FeatureManagement" />
    <PackageReference Include="CoverGo.Policies.Client" />
    <PackageReference Include="CoverGo.Products.Client" />
    <PackageReference Include="CoverGo.Users.Client" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.PoliciesV3.Domain\CoverGo.PoliciesV3.Domain.csproj" />
  </ItemGroup>
</Project>