using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Application.Services.Interfaces;

public interface IPolicyMemberUploadValidationErrorExporterService
{
    Task<byte[]> ExportErrorMembers(string policyMemberUploadId, CancellationToken cancellationToken);
    Task<byte[]> ExportValidationErrors(string policyMemberUploadId, CancellationToken cancellationToken);

    /// <summary>
    /// Persists validation errors to the database using batch insertion for efficiency.
    /// Only persists errors - successful validations do not create database records.
    /// </summary>
    /// <param name="validationErrors">Collection of validation errors to persist</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PersistValidationErrorsAsync(ICollection<PolicyMemberUploadValidationError> validationErrors, CancellationToken cancellationToken);
}

