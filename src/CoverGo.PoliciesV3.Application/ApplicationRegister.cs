using CoverGo.PoliciesV3.Application.Services;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;

namespace CoverGo.PoliciesV3.Application;

public static class ApplicationRegister
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // Register MediatR - building blocks package handles validation automatically
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ApplicationRegister).Assembly));

        // Register all validators from assembly
        services.AddValidatorsFromAssembly(typeof(ApplicationRegister).Assembly);

        // Register atomic specifications (individual business rules)
        RegisterAtomicSpecifications(services);

        // Register composite specifications (orchestrate multiple atomic specs)
        RegisterCompositeSpecifications(services);

        // Register supporting helper classes for composite specifications
        RegisterValidationHelpers(services);

        return services;
    }

    /// <summary>
    /// Register individual atomic specifications (business rules)
    /// </summary>
    private static void RegisterAtomicSpecifications(IServiceCollection services)
    {
        // Uniqueness specifications
        services.AddScoped<MemberMustHaveUniqueEmailSpecification>();
        services.AddScoped<MemberMustHaveUniqueHKIDSpecification>();
        services.AddScoped<MemberMustHaveUniquePassportSpecification>();
        services.AddScoped<MemberMustHaveUniqueStaffNumberSpecification>();

        // Upload-wide uniqueness specifications
        services.AddScoped<UploadMustHaveUniqueEmailsSpecification>();
        services.AddScoped<UploadMustHaveUniqueIdentificationSpecification>();
        services.AddScoped<UploadMustHaveUniqueMemberIdsSpecification>();

        // Member field specifications
        services.AddScoped<MemberEffectiveDateMustBeValidSpecification>();
        services.AddScoped<MemberFieldsMustMatchSchemaSpecification>();
        services.AddScoped<MemberIdMustFollowBusinessRulesSpecification>();
        services.AddScoped<MemberMustHaveValidPlanIdSpecification>();

        // Dependent and relationship specifications
        services.AddScoped<DependentMustHaveValidPrimaryMemberSpecification>();
        services.AddScoped<DependentAndEmployeeMustBeOnSamePlanSpecification>();
    }

    /// <summary>
    /// Register composite specifications that orchestrate multiple atomic specifications
    /// </summary>
    private static void RegisterCompositeSpecifications(IServiceCollection services)
    {
        // Main composite specifications
        services.AddScoped<CompleteUploadValidationSpecification>();
        services.AddScoped<IndividualMemberValidationSpecification>();
        services.AddScoped<UploadWideValidationSpecification>();
    }

    /// <summary>
    /// Register helper classes that support the composite specifications
    /// </summary>
    private static void RegisterValidationHelpers(IServiceCollection services)
    {
        // Helper for error aggregation
        services.AddScoped<IValidationErrorAggregator, ValidationErrorAggregator>();

        // Helper for concurrent member processing
        services.AddScoped<IConcurrentMemberProcessor, ParallelMemberProcessor>();

        // Helper for upload validation orchestration
        services.AddScoped<IUploadValidationOrchestrator, UploadValidationOrchestrator>();

        // Helper for validation data gathering
        services.AddScoped<PolicyMemberValidationDataService>();
    }
}
