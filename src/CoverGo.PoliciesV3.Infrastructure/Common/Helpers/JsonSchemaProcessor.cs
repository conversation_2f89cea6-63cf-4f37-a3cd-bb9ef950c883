using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Helpers;
using System.Text.Json;
using System.Text.Json.Serialization;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Infrastructure.Schemas;
using CoverGo.PoliciesV3.Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Helpers;

/// <summary>
/// Unified JSON schema processor that consolidates all JSON serialization and schema processing functionality.
/// </summary>
public static class JsonSchemaProcessor
{
    #region JSON Element Constants

    private static readonly Lazy<JsonElement> _emptyJsonElement = new(CreateEmptyJsonElement, LazyThreadSafetyMode.ExecutionAndPublication);

    public static JsonElement EmptyJsonElement => _emptyJsonElement.Value;

    private static JsonElement CreateEmptyJsonElement()
    {
        using var document = JsonDocument.Parse("{}");
        return document.RootElement.Clone();
    }

    #endregion

    #region JSON Serialization Options

    /// <summary>
    /// JsonSerializerOptions for database storage with snake_case property naming.
    /// </summary>
    public static readonly JsonSerializerOptions DatabaseOptions = new()
    {
        PropertyNamingPolicy = SnakeCaseNamingPolicy.Instance,
        WriteIndented = false,
        PropertyNameCaseInsensitive = true,
        Converters = { new JsonStringEnumConverter(SnakeCaseNamingPolicy.Instance) }
    };

    /// <summary>
    /// JsonSerializerOptions for external API with formula support.
    /// </summary>
    private static readonly JsonSerializerOptions ExternalApiOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        WriteIndented = false,
        Converters =
        {
            new JsonStringEnumConverter(JsonNamingPolicy.CamelCase),
            FormulaChildConverter.Instance
        }
    };

    /// <summary>
    /// JsonSerializerOptions for Products API with formula support.
    /// </summary>
    private static readonly JsonSerializerOptions ProductsApiOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        WriteIndented = false,
        Converters =
        {
            new JsonStringEnumConverter(),
            FormulaChildConverter.Instance
        }
    };

    #endregion

    #region Schema Processing Methods

    /// <summary>
    /// Processes contract holder census level fields from JsonElement.
    /// </summary>
    public static IReadOnlyList<PolicyMemberFieldDefinition> ProcessContractHolderMembersFields(
        JsonElement? fieldsJson, ILogger? logger = null) => fieldsJson == null
            ? []
            : ProcessSchema<ContractHoldersCensusLevelSchema>(
            fieldsJson.Value.GetRawText(),

            ExternalApiOptions,
            schema => schema.CensusLevel ?? [],
            ConvertCensusField,
            "ContractHolderCensusFields",
            logger);

    /// <summary>
    /// Processes product members fields from JSON string.
    /// </summary>
    public static IReadOnlyList<PolicyMemberFieldDefinition> ProcessProductMembersFields(
        string? schemaJson, ILogger? logger = null) => string.IsNullOrWhiteSpace(schemaJson)
            ? []
            : ProcessSchema<ProductsDataSchema>(
            schemaJson,
            ProductsApiOptions,
            schema => schema.MemberCustomSchema?.Properties?.Cast<object>() ?? [],
            ConvertCustomField,
            "ProductMembersFields",
            logger);

    /// <summary>
    /// Processes member fields from JsonElement.
    /// </summary>
    public static IReadOnlyList<PolicyMemberFieldDefinition> ProcessMembersFields(
        JsonElement? schemaJson, ILogger? logger = null) => schemaJson == null
            ? []
            : ProcessSchema<CasesDataSchema>(
            schemaJson.Value.GetRawText(),
            ExternalApiOptions,
            schema => schema.Properties?.Cast<object>() ?? [],
            ConvertCustomField,
            "MembersFields",
            logger);

    public static JsonElement ConvertSchemaObjectToJsonElement(object? schemaObject)
    {
        if (schemaObject == null) return EmptyJsonElement;

        try
        {
            string jsonString;

            if (schemaObject is JToken jToken)
            {
                jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(jToken);
            }
            else
            {
                jsonString = JsonSerializer.Serialize(schemaObject, ExternalApiOptions);
            }

            if (string.IsNullOrWhiteSpace(jsonString))
                return EmptyJsonElement;

            using var document = JsonDocument.Parse(jsonString);
            return document.RootElement.Clone();
        }
        catch (ArgumentException ex)
        {
            throw new JsonException("Failed to parse schema object as JSON.", ex);
        }
        catch (JsonException ex)
        {
            throw new JsonException("Failed to serialize Newtonsoft.Json object.", ex);
        }
    }

    #endregion

    #region Core Processing Engine

    private static IReadOnlyList<PolicyMemberFieldDefinition> ProcessSchema<TSchema>(
        string jsonString,
        JsonSerializerOptions options,
        Func<TSchema, IEnumerable<object>> fieldExtractor,
        Func<object, PolicyMemberFieldDefinition?> fieldConverter,
        string contextName,
        ILogger? logger)
    {
        logger?.LogInformation("Processing {ContextName} from JSON", contextName);

        try
        {
            TSchema? schema = JsonSerializer.Deserialize<TSchema>(jsonString, options);
            if (schema is null) return [];

            var results = new List<PolicyMemberFieldDefinition>();

            foreach (object field in fieldExtractor(schema))
            {
                try
                {
                    PolicyMemberFieldDefinition? fieldDefinition = fieldConverter(field);
                    if (fieldDefinition != null)
                    {
                        fieldDefinition = ProcessNestedObjectFieldParentReferences(fieldDefinition);
                        results.Add(fieldDefinition);
                    }
                }
                catch (Exception ex)
                {
                    string fieldName = field switch
                    {
                        ContractHoldersCensusLevelField censusField => censusField.Name ?? "Unknown",
                        KeyValuePair<string, PolicyMemberCustomField> kvp => kvp.Key,
                        _ => "Unknown"
                    };
                    logger?.LogWarning(ex, "{ContextName} - Error processing field {FieldName}, skipping",
                        contextName, fieldName);
                }
            }

            return results;
        }
        catch (JsonException ex)
        {
            logger?.LogWarning(ex, "Failed to deserialize {ContextName}, returning empty collection", contextName);
            return [];
        }
        catch (Exception ex)
        {
            logger?.LogWarning(ex, "Unexpected error processing {ContextName}, returning empty collection", contextName);
            return [];
        }
    }

    private static PolicyMemberFieldDefinition? ConvertCensusField(object field) =>
        field is ContractHoldersCensusLevelField censusField ? new()
        {
            Label = censusField.Name ?? string.Empty,
            Name = censusField.Name ?? string.Empty,
            Type = new StringFieldType
            {
                Options = censusField.Values?.Select(x => new StringOption { Value = x, Label = x }).ToList()
            },
            IsRequired = censusField.IsRequired,
            IsUnique = false,
        } : null;

    private static PolicyMemberFieldDefinition? ConvertCustomField(object field)
    {
        if (field is not KeyValuePair<string, PolicyMemberCustomField> kvp) return null;

        (string fieldName, PolicyMemberCustomField fieldType) = kvp;
        CustomFieldTypeBase? type = fieldType.ToCustomFieldType();

        return type == null ? null : new()
        {
            Label = fieldType.Meta?.Label ?? fieldName,
            Name = fieldName,
            Type = type,
            IsRequired = fieldType.IsRequired,
            IsUnique = fieldType.IsUnique,
            Condition = fieldType.Meta?.Condition,
            IsBenefitField = fieldType.IsBenefitField
        };
    }

    private static PolicyMemberFieldDefinition ProcessNestedObjectFieldParentReferences(PolicyMemberFieldDefinition fieldDefinition)
    {
        // Handle nested object fields - update parent references on inner field definitions
        if (fieldDefinition.Type is ObjectFieldType objType)
        {
            return fieldDefinition with
            {
                Type = objType with
                {
                    InnerFieldDefinitions = objType.InnerFieldDefinitions
                        .Select(innerField => innerField with { Parent = fieldDefinition })
                        .ToList()
                }
            };
        }

        return fieldDefinition;
    }

    #endregion
}

public sealed class SnakeCaseNamingPolicy : JsonNamingPolicy
{
    public static readonly SnakeCaseNamingPolicy Instance = new();
    private SnakeCaseNamingPolicy() { }

    public override string ConvertName(string name) =>
        DatabaseNamingHelpers.ConvertToSnakeCase(name);
}

public sealed class FormulaChildConverter : JsonConverter<PolicyMemberCustomFieldFormulaChild>
{
    public static readonly FormulaChildConverter Instance = new();
    private FormulaChildConverter() { }

    private static readonly Dictionary<string, Type> TypeMap = new()
    {
        [PolicyMemberCustomFieldFormulaValueChild.Discriminator] = typeof(PolicyMemberCustomFieldFormulaValueChild),
        [PolicyMemberCustomFieldFormulaDataChild.Discriminator] = typeof(PolicyMemberCustomFieldFormulaDataChild)
    };

    public override PolicyMemberCustomFieldFormulaChild Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        using var doc = JsonDocument.ParseValue(ref reader);
        JsonElement root = doc.RootElement;

        string discriminator = GetDiscriminator(root);

        PolicyMemberCustomFieldFormulaChild? knownTypeResult = TryDeserializeKnownType(discriminator, root, options);
        if (knownTypeResult != null)
            return knownTypeResult;

        if (discriminator == "formula")
            return ProcessFormulaChildren(root, options);

        throw new JsonException($"Unknown discriminator value '{discriminator}' for PolicyMemberCustomFieldFormulaChild");
    }

    private static string GetDiscriminator(JsonElement root)
    {
        if (!root.TryGetProperty("name", out JsonElement nameElement))
            throw new JsonException("Missing discriminator property 'name' for PolicyMemberCustomFieldFormulaChild");

        string? discriminator = nameElement.GetString();
        if (string.IsNullOrEmpty(discriminator))
            throw new JsonException("Empty discriminator value for PolicyMemberCustomFieldFormulaChild");

        return discriminator;
    }

    private static PolicyMemberCustomFieldFormulaChild? TryDeserializeKnownType(
        string discriminator, JsonElement root, JsonSerializerOptions options)
    {
        if (TypeMap.TryGetValue(discriminator, out Type? targetType))
            return (PolicyMemberCustomFieldFormulaChild?)JsonSerializer.Deserialize(root.GetRawText(), targetType, options);

        return null;
    }

    private static PolicyMemberCustomFieldFormulaChild ProcessFormulaChildren(JsonElement root, JsonSerializerOptions options)
    {
        if (!root.TryGetProperty("children", out JsonElement childrenElement) ||
            childrenElement.ValueKind != JsonValueKind.Array)
            return CreateFallbackDataChild();

        return childrenElement.EnumerateArray()
            .Select(child => TryProcessSingleChild(child, options))
            .OfType<PolicyMemberCustomFieldFormulaChild>()
            .FirstOrDefault() ?? CreateFallbackDataChild();
    }

    private static PolicyMemberCustomFieldFormulaChild? TryProcessSingleChild(JsonElement child, JsonSerializerOptions options)
    {
        if (!child.TryGetProperty("name", out JsonElement childNameElement))
            return null;

        string? childName = childNameElement.GetString();
        if (childName is not ("data" or "value"))
            return null;

        return (PolicyMemberCustomFieldFormulaChild?)JsonSerializer.Deserialize(
            child.GetRawText(), typeof(PolicyMemberCustomFieldFormulaChild), options);
    }

    private static PolicyMemberCustomFieldFormulaDataChild CreateFallbackDataChild() =>
        new()
        {
            Name = "data",
            Props = new PolicyMemberCustomFieldFormulaDataProperties { Path = "" }
        };

    public override void Write(Utf8JsonWriter writer, PolicyMemberCustomFieldFormulaChild value, JsonSerializerOptions options) =>
        JsonSerializer.Serialize(writer, value, value.GetType(), options);
}
