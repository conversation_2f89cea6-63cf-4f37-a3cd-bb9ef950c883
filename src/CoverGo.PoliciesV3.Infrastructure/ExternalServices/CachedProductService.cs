using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Caching;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.Products.Client;

namespace CoverGo.PoliciesV3.Infrastructure.ExternalServices;

/// <summary>
/// Cached decorator for IProductService that provides distributed caching
/// for product member schema data to improve performance and reduce external service calls.
/// </summary>
public class CachedProductService(
    ITenantProvider tenantProvider,
    IProductService inner,
    CacheProvider cache) : IProductService
{
    #region Cache Configuration

    private const string ProductMemberSchemaCacheKeyTemplate = "schema:product-member:{tenant}:{productId}";

    /// <summary>
    /// Cache TTL for product member schema data - 6 hours to match other schema caching patterns.
    /// Schema data is relatively static and can be cached for longer periods.
    /// </summary>
    private static readonly TimeSpan ProductMemberSchemaTtl = TimeSpan.FromHours(6);

    #endregion

    #region IProductService Implementation

    /// <summary>
    /// Gets the product member schema with caching support.
    /// Only caches non-null, non-empty schema strings to avoid caching error conditions.
    /// </summary>
    /// <param name="productId">The product ID to get the schema for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The product member schema JSON string, or null if not found</returns>
    public async Task<string?> GetProductMemberSchema(ProductId productId, CancellationToken cancellationToken)
    {
        if (!tenantProvider.TryGetCurrent(out TenantId? tenantId))
        {
            throw new InvalidOperationException("Current tenant is not set.");
        }

        string cacheKey = GetProductMemberSchemaCacheKey(tenantId.Value, productId);

        // Try to get from cache first
        string? cachedSchema = await cache.GetAsync<string>(cacheKey, cancellationToken);
        if (cachedSchema != null)
        {
            return cachedSchema;
        }

        // Not in cache, fetch from inner service
        string? schema = await inner.GetProductMemberSchema(productId, cancellationToken);

        // Only cache valid (non-null, non-empty) schemas
        // Don't cache null or empty values as they indicate error conditions that might be temporary
        if (!string.IsNullOrEmpty(schema))
        {
            await cache.SetAsync(cacheKey, schema, ProductMemberSchemaTtl, cancellationToken);
        }

        return schema;
    }

    /// <summary>
    /// Gets the product package type. This method is not cached and delegates directly to the inner service.
    /// </summary>
    /// <param name="productId">The product ID to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The product package type or null if not found</returns>
    public Task<string?> GetProductPackageType(ProductId productId, CancellationToken cancellationToken)
        => inner.GetProductPackageType(productId, cancellationToken);

    /// <summary>
    /// Gets available plan IDs for a product. This method is not cached and delegates directly to the inner service.
    /// </summary>
    /// <param name="productId">The product ID to get plan IDs for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available plan IDs or null if not found</returns>
    public Task<IReadOnlyList<string>?> GetAvailablePlanIds(
        Domain.Policies.ProductId productId,
        CancellationToken cancellationToken = default)
        => inner.GetAvailablePlanIds(productId, cancellationToken);

    #endregion

    #region Private Methods

    /// <summary>
    /// Generates a cache key for product member schema data.
    /// </summary>
    /// <param name="tenantId">The tenant ID for multi-tenant isolation</param>
    /// <param name="productId">The product ID containing plan, type, and version</param>
    /// <returns>A unique cache key for the product member schema</returns>
    private static string GetProductMemberSchemaCacheKey(string tenantId, ProductId productId)
    {
        return ProductMemberSchemaCacheKeyTemplate
            .Replace("{tenant}", tenantId)
            .Replace("{productId}", productId.ToString());
    }

    #endregion
}
