using System.Net.Sockets;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using CoverGo.Products.Client;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.SystemTextJson;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.ExternalServices;

public class ProductService(
    HttpClient httpClient,
    ILogger<ProductService> logger) : IProductService
{
    private readonly GraphQLHttpClient _productsGraphqlClient =
        new(
            new GraphQLHttpClientOptions
            {
                EndPoint = new Uri($"{httpClient.BaseAddress}graphql")
            },
            new SystemTextJsonSerializer(),
            httpClient);
    private readonly ILogger<ProductService> _logger = logger;

    public async Task<string?> GetProductMemberSchema(ProductId productId, CancellationToken cancellationToken)
    {
        products_Product? product = await GetProductByIdForSchema(productId, cancellationToken);
        if (product is null)
        {
            _logger.LogError("Product with ID {ProductId} not found.", productId);
            return null;
        }
        products_Script? pricingScript = product.scripts.FirstOrDefault(s => s.type == products_ScriptTypeEnum.PRICING_STANDARD)
                 ?? product.scripts.FirstOrDefault(s => s.type == products_ScriptTypeEnum.PRICING);
        if (pricingScript?.inputSchema != null) return pricingScript.inputSchema;
        _logger.LogError("PricingScript with input schema for product with ID {ProductId} not found.", productId);
        return null;
    }

    public async Task<string?> GetProductPackageType(ProductId productId, CancellationToken cancellationToken)
    {
        try
        {
            products_Product? product = await GetProductByIdForPackage(productId, cancellationToken);
            if (product is null)
            {
                _logger.LogWarning("Product with ID {ProductId} not found for package type check.", productId);
                return null;
            }

            // Extract package type from product fields
            // Look for "productPackage" field in the product.fields collection
            ProductField? packageField = product.fields?.FirstOrDefault(f => f.key == "productPackage");

            if (packageField?.value == null)
            {
                _logger.LogDebug("Product {ProductId} does not have 'productPackage' field", productId);
                return null;
            }

            string? packageType = packageField.value.ToString();
            _logger.LogDebug("Product {ProductId} has productPackage field with value: '{PackageType}'",
                productId, packageType ?? "null");

            return packageType;
        }
        catch (Exception ex) when (ex is InvalidOperationException)
        {
            // GraphQL business errors (e.g., product not found, invalid query)
            // These are expected business failures that should be handled gracefully
            _logger.LogWarning(ex, "Business error getting product package type for product {ProductId}: {Error}",
                productId, ex.Message);
            return null;
        }
        catch (Exception ex) when (ex is HttpRequestException or TaskCanceledException or SocketException or TimeoutException)
        {
            // Infrastructure failures that should cause explicit validation failure
            _logger.LogError(ex, "Infrastructure failure getting product package type for product {ProductId}. " +
                "This will cause dependent plan validation to fail explicitly to prevent silent validation bypass.", productId);
            throw;
        }
        catch (Exception ex)
        {
            // Unexpected exceptions should also cause explicit failure
            _logger.LogError(ex, "Unexpected error getting product package type for product {ProductId}. " +
                "This will cause dependent plan validation to fail explicitly to prevent silent validation bypass.", productId);
            throw;
        }
    }

    /// <summary>
    /// Gets product by ID with scripts for schema retrieval
    /// </summary>
    private async Task<products_Product?> GetProductByIdForSchema(ProductId productId, CancellationToken cancellationToken)
    {
        string query = new QueryBuilder().product(
            new QueryBuilder.productArgs(new products_ProductIdInput { plan = productId.Plan, version = productId.Version, type = productId.Type }),
            new products_ProductBuilder()
                        .id(new products_ProductIdBuilder().WithAllFields())
                        .scripts(new products_ProductBuilder.scriptsArgs(new products_ScriptWhereInput
                        {
                            or = [new products_ScriptWhereInput { type = products_ScriptTypeEnum.PRICING }, new() { type = products_ScriptTypeEnum.PRICING_STANDARD }]
                        }), new products_ScriptBuilder().WithAllFields())).Build();

        return await _productsGraphqlClient.SendQueryAndEnsureAsync<products_Product>(query, cancellationToken);
    }

    /// <summary>
    /// Gets product by ID with package information for SME type checking
    /// </summary>
    private async Task<products_Product?> GetProductByIdForPackage(ProductId productId, CancellationToken cancellationToken)
    {
        string query = new QueryBuilder().product(
            new QueryBuilder.productArgs(new() { plan = productId.Plan, version = productId.Version, type = productId.Type }),
            new products_ProductBuilder()
                        .id(new products_ProductIdBuilder().WithAllFields())
                        .fields(new ProductFieldBuilder().WithAllFields())
                        ).Build();

        return await _productsGraphqlClient.SendQueryAndEnsureAsync<products_Product>(query, cancellationToken);
    }

    public async Task<IReadOnlyList<string>?> GetAvailablePlanIds(
        Domain.Policies.ProductId productId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get available plan IDs using the correct GraphQL approach
            HashSet<string>? availablePlansSet = await GetPlanIdsFromGraphQlProductDetails(productId, cancellationToken);
            if (availablePlansSet is not null) return [.. availablePlansSet];
            _logger.LogWarning("Could not retrieve plan details for product {ProductId}", productId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available plan IDs for product {ProductId}", productId);
            return null;
        }
    }

    private async Task<HashSet<string>?> GetPlanIdsFromGraphQlProductDetails(Domain.Policies.ProductId domainProductId, CancellationToken cancellationToken)
    {
        var productIdInput = new products_ProductIdInput
        {
            plan = domainProductId.Plan,
            version = domainProductId.Version,
            type = domainProductId.Type
        };

        string queryString = new QueryBuilder()
            .productProductPlanDetails(
                new QueryBuilder.productProductPlanDetailsArgs(
                    new products_ProductPlanDetailFactorsInput { productId = productIdInput }
                ),
                new products_ProductPlanDetailsBuilder()
                    .planDetails(new products_ProductPlanDetailBuilder()
                        .id()
                    )
            ).Build();

        try
        {
            products_ProductPlanDetails? response = await _productsGraphqlClient
                .SendQueryAndEnsureAsync<products_ProductPlanDetails>(queryString, cancellationToken);

            if (response?.planDetails == null || response.planDetails.Count == 0)
            {
                _logger.LogWarning("productProductPlanDetails query returned no plan details for ProductId: {ProductId}", domainProductId);
                return null;
            }

            // Extract plan IDs from the response
            var planIds = response.planDetails
                .Where(plan => !string.IsNullOrEmpty(plan.id))
                .Select(plan => plan.id!)
                .ToHashSet();

            return planIds;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching plan details for ProductId: {ProductId}", domainProductId);
            throw;
        }
    }
}

