using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Infrastructure.PolicyMemberUploads;


internal class PolicyMemberUploadRepository(PostgreSqlUnitOfWork<ApplicationDbContext> unitOfWork, IUserContextProvider userContextProvider)
    : PostgreSqlRepository<PolicyMemberUpload, PolicyMemberUploadId, ApplicationDbContext>(unitOfWork, userContextProvider), IPolicyMemberUploadRepository
{

    /// <summary>
    /// Lock statuses that prevent upload modifications
    /// </summary>
    private static readonly PolicyMemberUploadStatus[] LockStatuses = [
        PolicyMemberUploadStatus.CANCELING,
        PolicyMemberUploadStatus.CANCELED
    ];


    /// <summary>
    /// Starts the validation process for an upload if it's not in a locked status.
    /// Updates status to VALIDATING and clears any existing validation errors.
    /// </summary>
    public async Task<bool> StartValidationIfNotLockedAsync(
        PolicyMemberUploadId id,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(id);

        try
        {
            // First, clear any existing validation errors for this upload
            // Use Entity property to access the validation errors through the same DbContext
            await Entity.Where(u => u.Id == id)
                .SelectMany(u => u.ValidationErrors)
                .ExecuteDeleteAsync(cancellationToken);

            // Then, update the upload status to VALIDATING
            int numUpdated = await Entity
                .Where(e => EF.Property<PolicyMemberUploadId?>(e, nameof(PolicyMemberUpload.Id)) != null &&
                            EF.Property<PolicyMemberUploadId>(e, nameof(PolicyMemberUpload.Id)).Equals(id))
                .Where(e => !LockStatuses.Contains(e.Status))
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.Status, PolicyMemberUploadStatus.VALIDATING)
                    .SetProperty(u => u.EntityAuditInfo.LastModifiedAt, DateTime.UtcNow),
                    cancellationToken);

            return numUpdated > 0;
        }
        catch (DbUpdateConcurrencyException)
        {
            return false;
        }
    }

    /// <summary>
    /// Completes the validation process for an upload if it's not in a locked status.
    /// Updates the final validation status and member counts.
    /// </summary>
    public async Task<bool> CompleteValidationIfNotLockedAsync(
        PolicyMemberUploadId id,
        PolicyMemberUploadStatus finalStatus,
        int validCount,
        int invalidCount,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(id);

        // Validate that finalStatus is a valid completion status
        if (finalStatus != PolicyMemberUploadStatus.VALIDATED &&
            finalStatus != PolicyMemberUploadStatus.VALIDATING_ERROR)
        {
            throw new ArgumentException($"Invalid final status: {finalStatus}. Must be VALIDATED or VALIDATING_ERROR.", nameof(finalStatus));
        }

        try
        {
            int numUpdated = await Entity
                .Where(e => EF.Property<PolicyMemberUploadId>(e, nameof(PolicyMemberUpload.Id)).Equals(id))
                .Where(e => !LockStatuses.Contains(e.Status))
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.Status, finalStatus)
                    .SetProperty(u => u.ValidMembersCount, validCount)
                    .SetProperty(u => u.InvalidMembersCount, invalidCount)
                    .SetProperty(u => u.EntityAuditInfo.LastModifiedAt, DateTime.UtcNow),
                    cancellationToken);

            return numUpdated > 0;
        }
        catch (DbUpdateConcurrencyException)
        {
            return false;
        }
    }

    /// <summary>
    /// Marks the validation as failed due to a system error if the upload is not in a locked status.
    /// Updates status to VALIDATING_ERROR and sets an error message.
    /// </summary>
    public async Task<bool> FailValidationIfNotLockedAsync(
        PolicyMemberUploadId id,
        string errorMessage,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(id);
        ArgumentException.ThrowIfNullOrWhiteSpace(errorMessage);

        try
        {
            int numUpdated = await Entity
                .Where(e => EF.Property<PolicyMemberUploadId>(e, nameof(PolicyMemberUpload.Id)).Equals(id))
                .Where(e => !LockStatuses.Contains(e.Status))
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.Status, PolicyMemberUploadStatus.VALIDATING_ERROR)
                    .SetProperty(u => u.EntityAuditInfo.LastModifiedAt, DateTime.UtcNow),
                    cancellationToken);

            return numUpdated > 0;
        }
        catch (DbUpdateConcurrencyException)
        {
            return false;
        }
    }

    /// <summary>
    /// Updates the validation progress for an upload if it's not in a locked status.
    /// Used during the validation process to update member counts and status.
    /// </summary>
    public async Task<bool> UpdateValidationProgressIfNotLockedAsync(
        PolicyMemberUploadId id,
        PolicyMemberUploadStatus status,
        int validCount,
        int invalidCount,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(id);

        try
        {
            int numUpdated = await Entity
                .Where(e => EF.Property<PolicyMemberUploadId>(e, nameof(PolicyMemberUpload.Id)).Equals(id))
                .Where(e => !LockStatuses.Contains(e.Status))
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.Status, status)
                    .SetProperty(u => u.ValidMembersCount, validCount)
                    .SetProperty(u => u.InvalidMembersCount, invalidCount)
                    .SetProperty(u => u.EntityAuditInfo.LastModifiedAt, DateTime.UtcNow),
                    cancellationToken);

            return numUpdated > 0;
        }
        catch (DbUpdateConcurrencyException)
        {
            return false;
        }
    }
}
