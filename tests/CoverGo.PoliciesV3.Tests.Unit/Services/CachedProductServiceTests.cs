using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Infrastructure.ExternalServices;
using CoverGo.Products.Client;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Unit tests for CachedProductService to verify service behavior and method delegation.
/// Note: Full caching behavior testing requires integration tests due to CacheProvider being a concrete class.
/// These tests focus on the service logic, tenant validation, and method delegation patterns.
/// </summary>
[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "Product")]
public class CachedProductServiceTests : IDisposable
{
    private readonly Mock<ITenantProvider> _mockTenantProvider;
    private readonly Mock<IProductService> _mockInnerService;
    private readonly Fixture _fixture;
    private readonly CancellationToken _cancellationToken;

    public CachedProductServiceTests()
    {
        _mockTenantProvider = new Mock<ITenantProvider>();
        _mockInnerService = new Mock<IProductService>();
        _fixture = new Fixture();
        _cancellationToken = CancellationToken.None;
    }

    #region Test Helpers

    /// <summary>
    /// Creates a CachedProductService instance for testing.
    /// Note: Uses a null cache provider since CacheProvider cannot be mocked.
    /// This limits testing to non-caching behavior.
    /// </summary>
    private CachedProductService CreateServiceWithoutCache() => new(
        _mockTenantProvider.Object,
        _mockInnerService.Object,
        null!); // CacheProvider cannot be mocked, so we pass null for non-cache tests

    private ProductId CreateProductId() => new()
    {
        Plan = _fixture.Create<string>(),
        Type = _fixture.Create<string>(),
        Version = _fixture.Create<string>()
    };

    private DomainProductId CreateDomainProductId() => new(
        _fixture.Create<string>(),
        _fixture.Create<string>(),
        _fixture.Create<string>());

    private TenantId CreateTenantId() => new(_fixture.Create<string>());

    private string CreateValidSchema() => """
        {
            "type": "object",
            "properties": {
                "name": { "type": "string", "required": true },
                "age": { "type": "number", "minimum": 0 },
                "email": { "type": "string", "format": "email" }
            }
        }
        """;

    private void SetupTenantProvider(TenantId? tenantId = null)
    {
        tenantId ??= CreateTenantId();
        _mockTenantProvider
            .Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? tenant) =>
            {
                tenant = tenantId;
                return true;
            });
    }

    private void SetupTenantProviderFailure()
    {
        _mockTenantProvider
            .Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? tenant) =>
            {
                tenant = null;
                return false;
            });
    }

    #endregion

    #region Service Instantiation Tests

    [Fact]
    public void CachedProductService_ShouldImplementIProductService()
    {
        // Arrange & Act
        CachedProductService service = CreateServiceWithoutCache();

        // Assert - Service should implement the interface correctly
        service.Should().BeAssignableTo<IProductService>();

        // Verify service has the expected methods
        Type serviceType = service.GetType();
        serviceType.GetMethod(nameof(IProductService.GetProductMemberSchema)).Should().NotBeNull();
        serviceType.GetMethod(nameof(IProductService.GetProductPackageType)).Should().NotBeNull();
        serviceType.GetMethod(nameof(IProductService.GetAvailablePlanIds)).Should().NotBeNull();
    }

    #endregion

    #region GetProductMemberSchema - Tenant Validation Tests

    [Fact]
    public async Task GetProductMemberSchema_WithNoTenant_ShouldThrowInvalidOperationException()
    {
        // Arrange
        ProductId productId = CreateProductId();
        SetupTenantProviderFailure(); // No tenant available

        CachedProductService service = CreateServiceWithoutCache();

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => service.GetProductMemberSchema(productId, _cancellationToken));

        exception.Message.Should().Be("Current tenant is not set.");

        // Verify no inner service calls were made
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    #endregion



    #region Method Delegation Tests

    [Fact]
    public async Task GetProductPackageType_ShouldAlwaysDelegateToInnerService()
    {
        // Arrange
        ProductId productId = CreateProductId();
        string expectedPackageType = "sme";

        _mockInnerService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ReturnsAsync(expectedPackageType);

        CachedProductService service = CreateServiceWithoutCache();

        // Act
        string? result = await service.GetProductPackageType(productId, _cancellationToken);

        // Assert
        result.Should().Be(expectedPackageType);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductPackageType(productId, _cancellationToken),
            Times.Once);

        // Verify no tenant provider calls were made (this method doesn't require tenant)
        _mockTenantProvider.Verify(
            x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny),
            Times.Never);
    }

    [Fact]
    public async Task GetProductPackageType_WithNullResult_ShouldReturnNullFromInnerService()
    {
        // Arrange
        ProductId productId = CreateProductId();

        _mockInnerService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ReturnsAsync((string?)null);

        CachedProductService service = CreateServiceWithoutCache();

        // Act
        string? result = await service.GetProductPackageType(productId, _cancellationToken);

        // Assert
        result.Should().BeNull();

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductPackageType(productId, _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetProductPackageType_WithException_ShouldPropagateExceptionFromInnerService()
    {
        // Arrange
        ProductId productId = CreateProductId();
        var expectedException = new InvalidOperationException("Package type service unavailable");

        _mockInnerService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ThrowsAsync(expectedException);

        CachedProductService service = CreateServiceWithoutCache();

        // Act & Assert
        InvalidOperationException thrownException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => service.GetProductPackageType(productId, _cancellationToken));

        thrownException.Should().Be(expectedException);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductPackageType(productId, _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetAvailablePlanIds_ShouldAlwaysDelegateToInnerService()
    {
        // Arrange
        DomainProductId domainProductId = CreateDomainProductId();
        IReadOnlyList<string> expectedPlanIds = new List<string> { "plan1", "plan2", "plan3" };

        _mockInnerService
            .Setup(x => x.GetAvailablePlanIds(domainProductId, _cancellationToken))
            .ReturnsAsync(expectedPlanIds);

        CachedProductService service = CreateServiceWithoutCache();

        // Act
        IReadOnlyList<string>? result = await service.GetAvailablePlanIds(domainProductId, _cancellationToken);

        // Assert
        result.Should().BeEquivalentTo(expectedPlanIds);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetAvailablePlanIds(domainProductId, _cancellationToken),
            Times.Once);

        // Verify no tenant provider calls were made (this method doesn't require tenant)
        _mockTenantProvider.Verify(
            x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny),
            Times.Never);
    }

    [Fact]
    public async Task GetAvailablePlanIds_WithNullResult_ShouldReturnNullFromInnerService()
    {
        // Arrange
        DomainProductId domainProductId = CreateDomainProductId();

        _mockInnerService
            .Setup(x => x.GetAvailablePlanIds(domainProductId, _cancellationToken))
            .ReturnsAsync((IReadOnlyList<string>?)null);

        CachedProductService service = CreateServiceWithoutCache();

        // Act
        IReadOnlyList<string>? result = await service.GetAvailablePlanIds(domainProductId, _cancellationToken);

        // Assert
        result.Should().BeNull();

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetAvailablePlanIds(domainProductId, _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetAvailablePlanIds_WithException_ShouldPropagateExceptionFromInnerService()
    {
        // Arrange
        DomainProductId domainProductId = CreateDomainProductId();
        var expectedException = new InvalidOperationException("Plan IDs service unavailable");

        _mockInnerService
            .Setup(x => x.GetAvailablePlanIds(domainProductId, _cancellationToken))
            .ThrowsAsync(expectedException);

        CachedProductService service = CreateServiceWithoutCache();

        // Act & Assert
        InvalidOperationException thrownException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => service.GetAvailablePlanIds(domainProductId, _cancellationToken));

        thrownException.Should().Be(expectedException);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetAvailablePlanIds(domainProductId, _cancellationToken),
            Times.Once);
    }

    #endregion



    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            // Clean up any resources if needed
        }
    }

    void IDisposable.Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
