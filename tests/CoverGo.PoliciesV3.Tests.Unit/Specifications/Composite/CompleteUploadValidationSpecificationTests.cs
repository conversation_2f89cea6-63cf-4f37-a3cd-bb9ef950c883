using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.Common;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Composite;

/// <summary>
/// Unit tests for CompleteUploadValidationSpecification.
/// Tests the composite specification that orchestrates upload-wide and individual member validations.
/// </summary>
public class CompleteUploadValidationSpecificationTests
{
    #region Test Setup

    private readonly Mock<UploadWideValidationSpecification> _mockUploadWideSpec;
    private readonly Mock<IndividualMemberValidationSpecification> _mockIndividualMemberSpec;
    private readonly Mock<IValidationErrorAggregator> _mockErrorAggregator;
    private readonly Mock<ILogger<CompleteUploadValidationSpecification>> _mockLogger;
    private readonly CompleteUploadValidationSpecification _specification;

    public CompleteUploadValidationSpecificationTests()
    {
        // Mock the interfaces and specifications
        _mockErrorAggregator = new Mock<IValidationErrorAggregator>();
        _mockLogger = new Mock<ILogger<CompleteUploadValidationSpecification>>();

        // Create mocks for the constituent specifications using CallBase = true
        _mockUploadWideSpec = new Mock<UploadWideValidationSpecification>(
                new Mock<UploadMustHaveUniqueEmailsSpecification>(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>()).Object,
                new Mock<UploadMustHaveUniqueIdentificationSpecification>(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>()).Object,
                new Mock<UploadMustHaveUniqueMemberIdsSpecification>(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>()).Object,
                new Mock<DependentAndEmployeeMustBeOnSamePlanSpecification>(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>()).Object,
                new Mock<IUploadValidationOrchestrator>().Object,
                new Mock<ILogger<UploadWideValidationSpecification>>().Object)
        { CallBase = true };

        _mockIndividualMemberSpec = new Mock<IndividualMemberValidationSpecification>(
                new Mock<MemberMustHaveUniqueEmailSpecification>(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>()).Object,
                new Mock<MemberMustHaveUniqueHKIDSpecification>(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>()).Object,
                new Mock<MemberMustHaveUniquePassportSpecification>(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>()).Object,
                new Mock<MemberMustHaveUniqueStaffNumberSpecification>(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>()).Object,
                new Mock<MemberIdMustFollowBusinessRulesSpecification>(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>()).Object,
                new Mock<MemberFieldsMustMatchSchemaSpecification>(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>()).Object,
                new Mock<MemberEffectiveDateMustBeValidSpecification>(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>()).Object,
                new Mock<DependentMustHaveValidPrimaryMemberSpecification>(Mock.Of<IPolicyMemberQueryService>(), Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>()).Object,
                new Mock<MemberMustHaveValidPlanIdSpecification>(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>()).Object,
                new Mock<IConcurrentMemberProcessor>().Object,
                new Mock<ILogger<IndividualMemberValidationSpecification>>().Object)
        { CallBase = true };

        // Create the real specification with mocked dependencies
        _specification = new CompleteUploadValidationSpecification(
            _mockUploadWideSpec.Object,
            _mockIndividualMemberSpec.Object,
            _mockErrorAggregator.Object,
            _mockLogger.Object);
    }

    #endregion

    #region Business Rule Properties Tests

    [Fact]
    public void BusinessRuleName_ShouldReturnExpectedValue()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        businessRuleName.Should().Be("Complete Upload Validation");
    }

    [Fact]
    public void Description_ShouldReturnExpectedValue()
    {
        // Act
        string description = _specification.Description;

        // Assert
        description.Should().Be("Validates entire policy member upload including file processing, upload-wide rules, and individual member validation");
    }

    #endregion

    #region ValidateBatchAsync Tests

    [Fact]
    public async Task ValidateBatchAsync_WithValidData_ShouldReturnSuccessResult()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var uploadWideResult = BatchValidationResult.Success(10);
        var individualMemberResult = BatchValidationResult.Success(10);
        var aggregatedResult = BatchValidationResult.Success(10);

        // Mock the constituent specifications
        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(uploadWideResult);
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individualMemberResult);
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(aggregatedResult);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.ValidCount.Should().Be(10);
        result.InvalidCount.Should().Be(0);
        result.RowErrors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateBatchAsync_WithUploadWideErrors_ShouldReturnErrorResult()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var uploadWideErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] = [CreateValidationError("DUPLICATE_EMAIL", "email")],
            [1] = [CreateValidationError("DUPLICATE_ID", "hkid")]
        };
        var uploadWideResult = BatchValidationResult.WithErrors(8, 2, uploadWideErrors);
        var individualMemberResult = BatchValidationResult.Success(10);
        var aggregatedResult = BatchValidationResult.WithErrors(8, 2, uploadWideErrors);

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(uploadWideResult);
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individualMemberResult);
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(aggregatedResult);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(8);
        result.InvalidCount.Should().Be(2);
        result.RowErrors.Should().HaveCount(2);
        result.RowErrors.Should().ContainKey(0);
        result.RowErrors.Should().ContainKey(1);
    }

    [Fact]
    public async Task ValidateBatchAsync_WithIndividualMemberErrors_ShouldReturnErrorResult()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var memberErrors = new Dictionary<int, List<ValidationError>>
        {
            [2] = [CreateValidationError("INVALID_PLAN", "planId")],
            [3] = [CreateValidationError("INVALID_DATE", "effectiveDate")]
        };
        var uploadWideResult = BatchValidationResult.Success(10);
        var individualMemberResult = BatchValidationResult.WithErrors(8, 2, memberErrors);
        var aggregatedResult = BatchValidationResult.WithErrors(8, 2, memberErrors);

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(uploadWideResult);
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individualMemberResult);
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(aggregatedResult);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(8);
        result.InvalidCount.Should().Be(2);
        result.RowErrors.Should().HaveCount(2);
        result.RowErrors.Should().ContainKey(2);
        result.RowErrors.Should().ContainKey(3);
    }

    [Fact]
    public async Task ValidateBatchAsync_WithBothTypesOfErrors_ShouldReturnCombinedErrorResult()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var uploadWideErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] = [CreateValidationError("DUPLICATE_EMAIL", "email")]
        };
        var memberErrors = new Dictionary<int, List<ValidationError>>
        {
            [1] = [CreateValidationError("INVALID_PLAN", "planId")]
        };
        var combinedErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] = [CreateValidationError("DUPLICATE_EMAIL", "email")],
            [1] = [CreateValidationError("INVALID_PLAN", "planId")]
        };

        var uploadWideResult = BatchValidationResult.WithErrors(9, 1, uploadWideErrors);
        var individualMemberResult = BatchValidationResult.WithErrors(9, 1, memberErrors);
        var aggregatedResult = BatchValidationResult.WithErrors(8, 2, combinedErrors);

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(uploadWideResult);
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individualMemberResult);
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(aggregatedResult);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(8);
        result.InvalidCount.Should().Be(2);
        result.RowErrors.Should().HaveCount(2);
        result.RowErrors.Should().ContainKey(0);
        result.RowErrors.Should().ContainKey(1);
    }

    #endregion

    #region ValidateAsync Tests

    [Fact]
    public async Task ValidateAsync_WithValidData_ShouldReturnSuccessfulOrchestrationResult()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var batchResult = BatchValidationResult.Success(10);

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(batchResult);

        // Act
        ValidationOrchestrationResult result = await _specification.ProcessUploadComplianceAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccessful.Should().BeTrue();
        result.ValidMembersCount.Should().Be(10);
        result.InvalidMembersCount.Should().Be(0);
        result.MemberErrors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateAsync_WithErrors_ShouldReturnOrchestrationResultWithErrors()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var errors = new Dictionary<int, List<ValidationError>>
        {
            [0] = [CreateValidationError("TEST_ERROR", "field")]
        };
        var batchResult = BatchValidationResult.WithErrors(9, 1, errors);

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(batchResult);
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(batchResult);

        // Act
        ValidationOrchestrationResult result = await _specification.ProcessUploadComplianceAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccessful.Should().BeTrue(); // OrchestrationResult.IsSuccessful is always true unless there's an exception
        result.ValidMembersCount.Should().Be(9);
        result.InvalidMembersCount.Should().Be(1);
        result.MemberErrors.Should().HaveCount(1);
        result.HasValidationErrors.Should().BeTrue();
    }

    #endregion

    #region Exception Handling Tests

    [Fact]
    public async Task ValidateBatchAsync_WithMinimalPreProcessedData_ShouldReturnSuccess()
    {
        // Arrange
        CompleteValidationContext context = CreateContextWithoutPreProcessedData();

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.ValidCount.Should().Be(0, "empty data should result in 0 valid members");
        result.InvalidCount.Should().Be(0, "empty data should result in 0 invalid members");
        result.RowErrors.Should().BeEmpty("empty data should have no row errors");
    }

    [Fact]
    public async Task ValidateBatchAsync_WithInvalidFileProcessing_ShouldThrowBadFileContentException()
    {
        // Arrange
        CompleteValidationContext context = CreateContextWithInvalidFileProcessing();

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _specification.EvaluateBusinessRulesAsync(context));

        exception.Message.Should().Contain("Error processing upload file");
        exception.Message.Should().Contain("Invalid file format - missing required columns");
    }

    [Fact]
    public async Task ValidateBatchAsync_WhenUploadWideSpecThrowsException_ShouldPropagateException()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var expectedException = new InvalidOperationException("Upload-wide validation failed");

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException actualException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _specification.EvaluateBusinessRulesAsync(context));

        actualException.Should().Be(expectedException);
    }

    [Fact]
    public async Task ValidateBatchAsync_WhenIndividualMemberSpecThrowsException_ShouldPropagateException()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var expectedException = new InvalidOperationException("Individual member validation failed");

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException actualException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _specification.EvaluateBusinessRulesAsync(context));

        actualException.Should().Be(expectedException);
    }

    [Fact]
    public async Task ValidateBatchAsync_WhenErrorAggregatorThrowsException_ShouldPropagateException()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var expectedException = new InvalidOperationException("Error aggregation failed");

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Throws(expectedException);

        // Act & Assert
        InvalidOperationException actualException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _specification.EvaluateBusinessRulesAsync(context));

        actualException.Should().Be(expectedException);
    }

    [Fact]
    public async Task ValidateAsync_WhenValidateBatchAsyncThrowsException_ShouldLogAndRethrow()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();
        var expectedException = new InvalidOperationException("Validation failed");

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException actualException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _specification.ProcessUploadComplianceAsync(context));

        actualException.Should().Be(expectedException);

        // Verify logging occurred
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error in complete upload validation")),
                expectedException,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    #endregion

    #region Edge Cases Tests

    [Fact]
    public async Task ValidateBatchAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _specification.EvaluateBusinessRulesAsync(null!));

    [Fact]
    public async Task ValidateAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _specification.ProcessUploadComplianceAsync(null!));

    [Fact]
    public async Task ValidateBatchAsync_WithEmptyMemberData_ShouldReturnSuccessWithZeroCounts()
    {
        // Arrange
        CompleteValidationContext context = CreateContextWithEmptyMemberData();
        var uploadWideResult = BatchValidationResult.Success(0);
        var individualMemberResult = BatchValidationResult.Success(0);
        var aggregatedResult = BatchValidationResult.Success(0);

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(uploadWideResult);
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individualMemberResult);
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(aggregatedResult);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.ValidCount.Should().Be(0);
        result.InvalidCount.Should().Be(0);
        result.RowErrors.Should().BeEmpty();
    }

    #endregion

    #region Verification Tests

    [Fact]
    public async Task ValidateBatchAsync_ShouldCallBothSpecificationsWithCorrectContexts()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(BatchValidationResult.Success(10));

        // Act
        await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        _mockUploadWideSpec.Verify(
            x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()),
            Times.Once);
        _mockIndividualMemberSpec.Verify(
            x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()),
            Times.Once);
        _mockErrorAggregator.Verify(
            x => x.AggregateResults(It.Is<List<BatchValidationResult>>(list => list.Count == 2), It.IsAny<int>()),
            Times.Once);
    }

    [Fact]
    public async Task ValidateBatchAsync_ShouldLogValidationStart()
    {
        // Arrange
        CompleteValidationContext context = CreateValidCompleteValidationContext();

        _mockUploadWideSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<UploadWideValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockIndividualMemberSpec.Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(BatchValidationResult.Success(10));
        _mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns(BatchValidationResult.Success(10));

        // Act
        await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Starting complete upload validation")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    #endregion

    #region Helper Methods

    private static CompleteValidationContext CreateValidCompleteValidationContext()
    {
        var upload = PolicyMemberUpload.Create(
            PolicyId.New,
            "test-upload.xlsx",
            10);

        var policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };

        ResolvedValidationData resolvedData = CreateTestResolvedValidationData();
        FileProcessingResult fileResult = CreateValidFileProcessingResult();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        MembersUploadFields transformedData = MemberUploadTestDataBuilder.SmallDataset().BuildMembersUploadFields();

        return CompleteValidationContext.CreateWithProcessedData(
            upload, policy, resolvedData, fileResult, schema, transformedData);
    }

    private static FileProcessingResult CreateValidFileProcessingResult()
    {
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { ["memberId"] = "EMP001", ["email"] = "<EMAIL>" },
            new Dictionary<string, string?> { ["memberId"] = "EMP002", ["email"] = "<EMAIL>" }
        };

        return FileProcessingResult.Success(memberData, 1024);
    }

    private static CompleteValidationContext CreateContextWithoutPreProcessedData()
    {
        var upload = PolicyMemberUpload.Create(
            PolicyId.New,
            "test-upload.xlsx",
            0);

        var policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = TestDateConstants.PolicyDates.PolicyStart,
            EndDate = TestDateConstants.PolicyDates.PolicyEnd,
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };

        // Create context with minimal data to test missing pre-processed data scenario
        var resolvedData = new ResolvedValidationData();
        var fileResult = FileProcessingResult.Success([], 0);
        var schema = new PolicyMemberFieldsSchema([]);
        var transformedData = MembersUploadFields.Empty();

        return CompleteValidationContext.CreateWithProcessedData(
            upload, policy, resolvedData, fileResult, schema, transformedData);
    }

    private static CompleteValidationContext CreateContextWithInvalidFileProcessing()
    {
        var upload = PolicyMemberUpload.Create(
            PolicyId.New,
            "test-upload.xlsx",
            0);

        var policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };

        ResolvedValidationData resolvedData = CreateTestResolvedValidationData();
        var invalidFileResult = FileProcessingResult.Failure(
            "Invalid file format - missing required columns",
            new InvalidOperationException("File processing failed"));
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        MembersUploadFields transformedData = MemberUploadTestDataBuilder.SmallDataset().BuildMembersUploadFields();

        return CompleteValidationContext.CreateWithProcessedData(
            upload, policy, resolvedData, invalidFileResult, schema, transformedData);
    }

    private static CompleteValidationContext CreateContextWithEmptyMemberData()
    {
        var upload = PolicyMemberUpload.Create(
            PolicyId.New,
            "empty-upload.xlsx",
            0);

        var policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };

        ResolvedValidationData resolvedData = CreateTestResolvedValidationData();
        var fileResult = FileProcessingResult.Success([], 0); // Empty member data
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        var emptyTransformedData = MembersUploadFields.Empty(); // Empty member data

        return CompleteValidationContext.CreateWithProcessedData(
            upload, policy, resolvedData, fileResult, schema, emptyTransformedData);
    }

    private static ResolvedValidationData CreateTestResolvedValidationData() => new()
    {
        UseTheSamePlanForEmployeeAndDependents = false,
        OnlyApplyForSmeProducts = false,
        AllowMembersFromOtherContractHolders = false,
        AvailablePlans = new HashSet<string> { "PLAN-001", "PLAN-002", "PLAN-003" },
        IsProductSme = false,
        ContractHolderPolicyIds = ["POL-001", "POL-002"],
        ValidEndorsementIds = [],
        TenantId = "tenant-123",
        DependentMembersCache = new Dictionary<string, PoliciesV3.Domain.PolicyMembers.PolicyMember?>(),
        ExistingIndividualIds = new HashSet<string>(),
        ExistingPolicyMembers = new Dictionary<string, PoliciesV3.Domain.PolicyMembers.PolicyMember?>(),
        MemberValidationStates = new Dictionary<string, IReadOnlyList<PoliciesV3.Domain.PolicyMembers.PolicyMember>>(),
        IndividualExistenceMap = new Dictionary<string, bool>()
    };

    private static PolicyMemberFieldsSchema CreateTestSchema() => new([]);

    private static ValidationError CreateValidationError(string code, string propertyPath) => new(code, propertyPath, propertyPath, new Dictionary<string, object?>());

    #endregion
}