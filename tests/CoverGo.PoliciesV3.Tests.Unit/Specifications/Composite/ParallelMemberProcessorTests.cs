using System.Collections.Concurrent;
using System.Diagnostics;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using FluentAssertions;
using Xunit;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Composite;

/// <summary>
/// Tests for ParallelMemberProcessor to validate the refactored concurrency logic
/// </summary>
public class ParallelMemberProcessorTests
{
    [Fact]
    public async Task ProcessMembersAsync_WithValidMembers_ShouldReturnEmptyErrorDictionary()
    {
        // Arrange
        var processor = new ParallelMemberProcessor(maxDegreeOfParallelism: 2);
        const int memberCount = 5;

        // Act
        var result = await processor.ProcessMembersAsync(
            memberCount,
            memberIndex => Task.FromResult(new List<ValidationError>()),
            CancellationToken.None);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task ProcessMembersAsync_WithErrorsInSomeMembers_ShouldReturnCorrectErrorDictionary()
    {
        // Arrange
        var processor = new ParallelMemberProcessor(maxDegreeOfParallelism: 2);
        const int memberCount = 5;
        var expectedErrors = new Dictionary<int, List<ValidationError>>
        {
            [1] = [new ValidationError("TEST_ERROR", "field1", "Field 1")],
            [3] = [new ValidationError("TEST_ERROR", "field3", "Field 3")]
        };

        // Act
        var result = await processor.ProcessMembersAsync(
            memberCount,
            memberIndex => Task.FromResult(expectedErrors.ContainsKey(memberIndex)
                ? expectedErrors[memberIndex]
                : new List<ValidationError>()),
            CancellationToken.None);

        // Assert
        result.Should().HaveCount(2);
        result.Should().ContainKey(1);
        result.Should().ContainKey(3);
        result[1].Should().HaveCount(1);
        result[3].Should().HaveCount(1);
        result[1][0].PropertyPath.Should().Be("field1");
        result[3][0].PropertyPath.Should().Be("field3");
    }

    [Fact]
    public async Task ProcessMembersAsync_WithCancellation_ShouldThrowOperationCanceledException()
    {
        // Arrange
        var processor = new ParallelMemberProcessor(maxDegreeOfParallelism: 2);
        const int memberCount = 100;
        using var cts = new CancellationTokenSource();

        // Act & Assert
        var act = async () =>
        {
            await processor.ProcessMembersAsync(
                memberCount,
                async memberIndex =>
                {
                    await Task.Delay(100, cts.Token); // Simulate work
                    return new List<ValidationError>();
                },
                cts.Token);
        };

        // Cancel after a short delay
        _ = Task.Run(async () =>
        {
            await Task.Delay(50);
            cts.Cancel();
        });

        await act.Should().ThrowAsync<OperationCanceledException>();
    }

    [Fact]
    public async Task ProcessMembersAsync_WithZeroMembers_ShouldReturnEmptyDictionary()
    {
        // Arrange
        var processor = new ParallelMemberProcessor(maxDegreeOfParallelism: 2);

        // Act
        var result = await processor.ProcessMembersAsync(
            0,
            memberIndex => Task.FromResult(new List<ValidationError>()),
            CancellationToken.None);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task ProcessMembersAsync_WithConcurrentAccess_ShouldBeThreadSafe()
    {
        // Arrange
        var processor = new ParallelMemberProcessor(maxDegreeOfParallelism: 4);
        const int memberCount = 50;
        var processedMembers = new ConcurrentBag<int>();

        // Act
        var result = await processor.ProcessMembersAsync(
            memberCount,
            memberIndex =>
            {
                processedMembers.Add(memberIndex);
                // Simulate some work
                Thread.Sleep(1);
                return Task.FromResult(new List<ValidationError>());
            },
            CancellationToken.None);

        // Assert
        result.Should().BeEmpty();
        processedMembers.Should().HaveCount(memberCount);
        processedMembers.Distinct().Should().HaveCount(memberCount);
        processedMembers.Should().OnlyContain(index => index >= 0 && index < memberCount);
    }

    [Fact]
    public void Constructor_WithoutMaxDegreeOfParallelism_ShouldUseDefaultConstant()
    {
        // Arrange & Act
        var processor = new ParallelMemberProcessor();

        // Use reflection to access the private field for testing
        var field = typeof(ParallelMemberProcessor).GetField("_maxDegreeOfParallelism",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var result = (int)field!.GetValue(processor)!;

        // Assert
        result.Should().Be(ValidationConstants.Concurrency.DefaultMaxConcurrentValidations);
    }

    [Fact]
    public async Task ProcessMembersAsync_WithCustomMaxDegreeOfParallelism_ShouldRespectLimit()
    {
        // Arrange
        const int maxDegreeOfParallelism = 2;
        var processor = new ParallelMemberProcessor(maxDegreeOfParallelism);
        const int memberCount = 10;
        var concurrentCount = 0;
        var maxConcurrentCount = 0;
        var lockObject = new object();

        // Act
        var result = await processor.ProcessMembersAsync(
            memberCount,
            async memberIndex =>
            {
                lock (lockObject)
                {
                    concurrentCount++;
                    maxConcurrentCount = Math.Max(maxConcurrentCount, concurrentCount);
                }

                await Task.Delay(50); // Simulate work

                lock (lockObject)
                {
                    concurrentCount--;
                }

                return new List<ValidationError>();
            },
            CancellationToken.None);

        // Assert
        result.Should().BeEmpty();
        maxConcurrentCount.Should().BeLessOrEqualTo(maxDegreeOfParallelism);
    }

    [Fact]
    public async Task ProcessMembersAsync_WithExceptionInValidator_ShouldPropagateException()
    {
        // Arrange
        var processor = new ParallelMemberProcessor(maxDegreeOfParallelism: 2);
        const int memberCount = 5;

        // Act & Assert
        var act = async () =>
        {
            await processor.ProcessMembersAsync(
                memberCount,
                memberIndex =>
                {
                    if (memberIndex == 2)
                        throw new InvalidOperationException("Test exception");
                    return Task.FromResult(new List<ValidationError>());
                },
                CancellationToken.None);
        };

        await act.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Test exception");
    }
}
