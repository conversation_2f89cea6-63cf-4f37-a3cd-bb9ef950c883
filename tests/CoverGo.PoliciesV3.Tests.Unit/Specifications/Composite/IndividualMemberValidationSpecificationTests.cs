using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Specifications.Composite;

/// <summary>
/// Unit tests for IndividualMemberValidationSpecification.
/// Tests the composite specification that validates individual member business rules.
/// </summary>
public class IndividualMemberValidationSpecificationTests
{
    #region Test Setup

    private readonly Mock<MemberMustHaveUniqueEmailSpecification> _mockMemberUniqueEmailSpec;
    private readonly Mock<MemberMustHaveUniqueHKIDSpecification> _mockMemberUniqueHkidSpec;
    private readonly Mock<MemberMustHaveUniquePassportSpecification> _mockMemberUniquePassportSpec;
    private readonly Mock<MemberMustHaveUniqueStaffNumberSpecification> _mockMemberUniqueStaffSpec;
    private readonly Mock<MemberIdMustFollowBusinessRulesSpecification> _mockMemberIdBusinessRulesSpec;
    private readonly Mock<MemberFieldsMustMatchSchemaSpecification> _mockMemberFieldsSchemaSpec;
    private readonly Mock<MemberEffectiveDateMustBeValidSpecification> _mockMemberEffectiveDateSpec;
    private readonly Mock<DependentMustHaveValidPrimaryMemberSpecification> _mockDependentValidationSpec;
    private readonly Mock<MemberMustHaveValidPlanIdSpecification> _mockMemberValidPlanIdSpec;
    private readonly Mock<IConcurrentMemberProcessor> _mockMemberProcessor;
    private readonly Mock<ILogger<IndividualMemberValidationSpecification>> _mockLogger;
    private readonly IndividualMemberValidationSpecification _specification;

    public IndividualMemberValidationSpecificationTests()
    {
        // Create mocks for all constituent specifications with their required dependencies
        _mockMemberUniqueEmailSpec = new Mock<MemberMustHaveUniqueEmailSpecification>(
            Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        _mockMemberUniqueHkidSpec = new Mock<MemberMustHaveUniqueHKIDSpecification>(
            Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        _mockMemberUniquePassportSpec = new Mock<MemberMustHaveUniquePassportSpecification>(
            Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        _mockMemberUniqueStaffSpec = new Mock<MemberMustHaveUniqueStaffNumberSpecification>(
            Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        _mockMemberIdBusinessRulesSpec = new Mock<MemberIdMustFollowBusinessRulesSpecification>(
            Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        _mockMemberFieldsSchemaSpec = new Mock<MemberFieldsMustMatchSchemaSpecification>(
            Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        _mockMemberEffectiveDateSpec = new Mock<MemberEffectiveDateMustBeValidSpecification>(
            Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        _mockDependentValidationSpec = new Mock<DependentMustHaveValidPrimaryMemberSpecification>(
            Mock.Of<IPolicyMemberQueryService>(), Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        _mockMemberValidPlanIdSpec = new Mock<MemberMustHaveValidPlanIdSpecification>(
            Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());
        _mockMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        _mockLogger = new Mock<ILogger<IndividualMemberValidationSpecification>>();

        // Use real ParallelMemberProcessor instead of mock for better integration testing
        var realMemberProcessor = new ParallelMemberProcessor(maxDegreeOfParallelism: 1);

        _specification = new IndividualMemberValidationSpecification(
            _mockMemberUniqueEmailSpec.Object,
            _mockMemberUniqueHkidSpec.Object,
            _mockMemberUniquePassportSpec.Object,
            _mockMemberUniqueStaffSpec.Object,
            _mockMemberIdBusinessRulesSpec.Object,
            _mockMemberFieldsSchemaSpec.Object,
            _mockMemberEffectiveDateSpec.Object,
            _mockDependentValidationSpec.Object,
            _mockMemberValidPlanIdSpec.Object,
            realMemberProcessor,
            _mockLogger.Object);
    }

    #endregion

    #region Business Rule Properties Tests

    [Fact]
    public void BusinessRuleName_ShouldReturnExpectedValue()
    {
        // Act
        string businessRuleName = _specification.BusinessRuleName;

        // Assert
        businessRuleName.Should().Be("Individual Member Validation");
    }

    [Fact]
    public void Description_ShouldReturnExpectedValue()
    {
        // Act
        string description = _specification.Description;

        // Assert
        description.Should().Be("Validates business rules that apply to individual members");
    }

    #endregion

    #region ValidateBatchAsync Tests

    [Fact]
    public void CreateValidIndividualMemberValidationContext_ShouldCreateValidContext()
    {
        // Arrange & Act
        IndividualMemberValidationContext context = CreateValidIndividualMemberValidationContext(memberCount: 5);

        // Assert
        context.Should().NotBeNull();
        context.MemberCount.Should().Be(5);
        context.MembersFields.Should().NotBeNull();
        context.MembersFields.Count.Should().Be(5);
        context.Policy.Should().NotBeNull();
        context.EndorsementId.Should().BeNull();
        context.ResolvedData.Should().NotBeNull();
        context.Schema.Should().NotBeNull();
    }

    [Fact]
    public async Task ValidateBatchAsync_WithValidMembers_ShouldReturnSuccessResult()
    {
        // Arrange
        IndividualMemberValidationContext context = CreateValidIndividualMemberValidationContext(memberCount: 5);
        SetupAllSpecificationsToSucceed();

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.ValidCount.Should().Be(5);
        result.InvalidCount.Should().Be(0);
        result.RowErrors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateBatchAsync_WithNoMembers_ShouldReturnSuccessWithZeroCount()
    {
        // Arrange
        IndividualMemberValidationContext context = CreateValidIndividualMemberValidationContext(memberCount: 0);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.ValidCount.Should().Be(0);
        result.InvalidCount.Should().Be(0);
        result.RowErrors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateBatchAsync_WithSchemaValidationErrors_ShouldReturnErrorResult()
    {
        // Arrange
        IndividualMemberValidationContext context = CreateValidIndividualMemberValidationContext(memberCount: 3);
        ValidationError schemaError = CreateValidationError("SCHEMA_MISMATCH", "firstName");

        _mockMemberFieldsSchemaSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Failure(schemaError));
        SetupOtherSpecificationsToSucceed();

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(0);
        result.InvalidCount.Should().Be(3);
        result.RowErrors.Should().HaveCount(3);
        result.RowErrors.Values.Should().AllSatisfy(errors =>
            errors.Should().Contain(e => e.Code == "SCHEMA_MISMATCH"));
    }

    [Fact]
    public async Task ValidateBatchAsync_WithUniquenessValidationErrors_ShouldReturnErrorResult()
    {
        // Arrange
        IndividualMemberValidationContext context = CreateValidIndividualMemberValidationContext(memberCount: 2);
        ValidationError emailError = CreateValidationError("DUPLICATE_EMAIL", "email");
        ValidationError hkidError = CreateValidationError("DUPLICATE_HKID", "hkid");

        SetupNonUniquenessSpecificationsToSucceed();
        _mockMemberUniqueEmailSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<UniquenessValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure(emailError));
        _mockMemberUniqueHkidSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<UniquenessValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure(hkidError));
        _mockMemberUniquePassportSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<UniquenessValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        _mockMemberUniqueStaffSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<UniquenessValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(0);
        result.InvalidCount.Should().Be(2);
        result.RowErrors.Should().HaveCount(2);
        result.RowErrors.Values.Should().AllSatisfy(errors =>
        {
            errors.Should().Contain(e => e.Code == "DUPLICATE_EMAIL");
            errors.Should().Contain(e => e.Code == "DUPLICATE_HKID");
        });
    }

    [Fact]
    public async Task ValidateBatchAsync_WithDependentMembers_ShouldValidateDependentRules()
    {
        // Arrange
        IndividualMemberValidationContext context = CreateContextWithDependentMembers();
        ValidationError dependentError = CreateValidationError("INVALID_PRIMARY_MEMBER", "primaryMemberId");

        // Debug: Check if any members are actually dependents
        bool hasAnyDependents = context.MembersFields.AsReadOnlyList().Any(m => m.IsDependent());
        hasAnyDependents.Should().BeTrue("Test data should contain dependent members");

        // Setup all specifications to succeed except dependent validation
        _mockMemberFieldsSchemaSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockMemberEffectiveDateSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockMemberIdBusinessRulesSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<MemberIdValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockMemberValidPlanIdSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<PlanValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        SetupUniquenessSpecificationsToSucceed();

        // Setup dependent validation to return failure
        _mockDependentValidationSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<DependentValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Failure(dependentError));

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();

        // Verify dependent validation was called
        _mockDependentValidationSpec.Verify(
            x => x.IsSatisfiedBy(It.IsAny<DependentValidationContext>(), CancellationToken.None),
            Times.AtLeastOnce);

        // Check if the dependent member (index 1) has validation errors
        result.IsValid.Should().BeFalse("Validation should fail when dependent validation fails");
        result.InvalidCount.Should().Be(1, "Only the dependent member should have validation errors");
        result.ValidCount.Should().Be(1, "The primary member should be valid");
        result.RowErrors.Should().ContainKey(1, "The dependent member (index 1) should have errors");
        result.RowErrors[1].Should().Contain(e => e.Code == "INVALID_PRIMARY_MEMBER", "Should contain the dependent validation error");
    }

    [Fact]
    public async Task ValidateBatchAsync_WithMultipleValidationErrors_ShouldCombineAllErrors()
    {
        // Arrange
        IndividualMemberValidationContext context = CreateValidIndividualMemberValidationContext(memberCount: 1);
        ValidationError schemaError = CreateValidationError("SCHEMA_ERROR", "field1");
        ValidationError dateError = CreateValidationError("DATE_ERROR", "effectiveDate");
        ValidationError planError = CreateValidationError("PLAN_ERROR", "planId");

        _mockMemberFieldsSchemaSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Failure(schemaError));
        _mockMemberEffectiveDateSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Failure(dateError));
        _mockMemberValidPlanIdSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<PlanValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Failure(planError));
        SetupRemainingSpecificationsToSucceed();

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.InvalidCount.Should().Be(1);
        result.RowErrors[0].Should().HaveCount(3);
        result.RowErrors[0].Should().Contain(e => e.Code == "SCHEMA_ERROR");
        result.RowErrors[0].Should().Contain(e => e.Code == "DATE_ERROR");
        result.RowErrors[0].Should().Contain(e => e.Code == "PLAN_ERROR");
    }

    #endregion

    #region Exception Handling Tests

    [Fact]
    public async Task ValidateBatchAsync_WithNullContext_ShouldThrowArgumentNullException() =>
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _specification.EvaluateBusinessRulesAsync(null!));

    [Fact]
    public void ValidateBatchAsync_WithNullResolvedData_ShouldThrowArgumentNullException()
    {
        // Arrange
        var upload = PolicyMemberUpload.Create(PolicyId.New, "test-upload.xlsx", 100);
        var policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        MembersUploadFields membersFields = MemberUploadTestDataBuilder.SmallDataset().BuildMembersUploadFields();

        // Act & Assert - Exception should be thrown during context creation with null resolved data
        Assert.Throws<ArgumentNullException>(() =>
            IndividualMemberValidationContext.Create(policy, membersFields, schema, null!, upload.EndorsementId));
    }

    [Fact]
    public async Task ValidateBatchAsync_WhenSpecificationThrowsException_ShouldReturnValidationError()
    {
        // Arrange
        IndividualMemberValidationContext context = CreateValidIndividualMemberValidationContext(memberCount: 1);
        var expectedException = new InvalidOperationException("Schema validation failed");

        _mockMemberFieldsSchemaSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .Throws(expectedException);

        // Act
        BatchValidationResult result = await _specification.EvaluateBusinessRulesAsync(context);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ValidCount.Should().Be(0);
        result.InvalidCount.Should().Be(1);
        result.RowErrors.Should().ContainKey(0);
        result.RowErrors[0].Should().HaveCount(1);
        result.RowErrors[0][0].Code.Should().Be("MEMBER_PROCESSING_FAILED");
    }

    #endregion

    #region Mock Setup Helper Methods

    private void SetupAllSpecificationsToSucceed()
    {
        SetupNonUniquenessSpecificationsToSucceed();
        SetupUniquenessSpecificationsToSucceed();
    }

    private void SetupNonUniquenessSpecificationsToSucceed()
    {
        _mockMemberFieldsSchemaSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockMemberEffectiveDateSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockMemberIdBusinessRulesSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<MemberIdValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockMemberValidPlanIdSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<PlanValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockDependentValidationSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<DependentValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
    }

    private void SetupUniquenessSpecificationsToSucceed()
    {
        _mockMemberUniqueEmailSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<UniquenessValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        _mockMemberUniqueHkidSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<UniquenessValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        _mockMemberUniquePassportSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<UniquenessValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        _mockMemberUniqueStaffSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<UniquenessValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
    }

    private void SetupOtherSpecificationsToSucceed()
    {
        _mockMemberEffectiveDateSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<FieldValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockMemberIdBusinessRulesSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<MemberIdValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockMemberValidPlanIdSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<PlanValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        SetupUniquenessSpecificationsToSucceed();
    }

    private void SetupRemainingSpecificationsToSucceed()
    {
        // Only setup specifications that are not already configured to fail
        _mockMemberIdBusinessRulesSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<MemberIdValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        _mockDependentValidationSpec.Setup(x => x.IsSatisfiedBy(It.IsAny<DependentValidationContext>(), CancellationToken.None))
            .ReturnsAsync(Result.Success());
        SetupUniquenessSpecificationsToSucceed();
    }

    #endregion

    #region Helper Methods

    private IndividualMemberValidationContext CreateValidIndividualMemberValidationContext(int memberCount)
    {
        var upload = PolicyMemberUpload.Create(
            PolicyId.New,
            "test-upload.xlsx",
            memberCount);

        var policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };

        ResolvedValidationData resolvedData = CreateTestResolvedValidationData();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        MembersUploadFields membersFields = memberCount > 0
            ? MemberUploadTestDataBuilder.Create().WithMemberCount(memberCount).BuildMembersUploadFields()
            : MembersUploadFields.Empty();

        return IndividualMemberValidationContext.Create(
            policy, membersFields, schema, resolvedData, upload.EndorsementId);
    }

    private IndividualMemberValidationContext CreateContextWithDependentMembers()
    {
        var upload = PolicyMemberUpload.Create(
            PolicyId.New,
            "test-upload.xlsx",
            2);

        var policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = false,
            Endorsements = [],
            ApprovedEndorsementIds = []
        };

        ResolvedValidationData resolvedData = CreateTestResolvedValidationData();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data manually to ensure we have dependents
        var memberDataList = new List<MemberUploadFields>
        {
            // Primary member (employee)
            new(new Dictionary<string, string?>
            {
                ["memberType"] = "employee",
                ["planId"] = "PLAN-001",
                ["effectiveDate"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["firstName"] = "John",
                ["lastName"] = "Doe",
                ["name"] = "John Doe",
                ["email"] = "<EMAIL>"
            }),
            // Dependent member
            new(new Dictionary<string, string?>
            {
                ["memberType"] = "dependent",
                ["planId"] = "PLAN-001",
                ["effectiveDate"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["firstName"] = "Jane",
                ["lastName"] = "Doe",
                ["name"] = "Jane Doe",
                ["email"] = "<EMAIL>",
                ["dependentOf"] = "John Doe"
            })
        };

        var membersFields = new MembersUploadFields(memberDataList);

        return IndividualMemberValidationContext.Create(
            policy, membersFields, schema, resolvedData, upload.EndorsementId);
    }

    private static ResolvedValidationData CreateTestResolvedValidationData() => new()
    {
        UseTheSamePlanForEmployeeAndDependents = false,
        OnlyApplyForSmeProducts = false,
        AllowMembersFromOtherContractHolders = false,
        AvailablePlans = new HashSet<string> { "PLAN-001", "PLAN-002", "PLAN-003" },
        IsProductSme = false,
        ContractHolderPolicyIds = ["POL-001", "POL-002"],
        ValidEndorsementIds = [],
        TenantId = "tenant-123",
        DependentMembersCache = new Dictionary<string, PoliciesV3.Domain.PolicyMembers.PolicyMember?>(),
        ExistingIndividualIds = new HashSet<string>(),
        ExistingPolicyMembers = new Dictionary<string, PoliciesV3.Domain.PolicyMembers.PolicyMember?>(),
        MemberValidationStates = new Dictionary<string, IReadOnlyList<PoliciesV3.Domain.PolicyMembers.PolicyMember>>(),
        IndividualExistenceMap = new Dictionary<string, bool>()
    };

    private static PolicyMemberFieldsSchema CreateTestSchema() => new([]);

    private static ValidationError CreateValidationError(string code, string propertyPath) => new(code, propertyPath, propertyPath, new Dictionary<string, object?>());

    #endregion
}