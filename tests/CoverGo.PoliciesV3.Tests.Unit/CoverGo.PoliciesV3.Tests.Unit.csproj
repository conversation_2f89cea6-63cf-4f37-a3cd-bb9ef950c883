<Project Sdk="Microsoft.NET.Sdk">

	<!-- Unit test specific dependencies not covered by Directory.Build.props -->
	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
		<PackageReference Include="FluentValidation" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\CoverGo.PoliciesV3.Api\CoverGo.PoliciesV3.Api.csproj" />
		<ProjectReference Include="..\..\src\CoverGo.PoliciesV3.Application\CoverGo.PoliciesV3.Application.csproj" />
		<ProjectReference Include="..\..\src\CoverGo.PoliciesV3.Domain\CoverGo.PoliciesV3.Domain.csproj" />
		<ProjectReference Include="..\..\src\CoverGo.PoliciesV3.Infrastructure\CoverGo.PoliciesV3.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Include="Files\**\*" CopyToOutputDirectory="PreserveNewest" />
	</ItemGroup>

</Project>