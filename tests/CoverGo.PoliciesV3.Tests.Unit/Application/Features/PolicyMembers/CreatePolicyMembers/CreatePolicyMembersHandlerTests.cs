using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.Users.Client;
using Microsoft.Extensions.Logging;
using CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMembers;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Products;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.CreatePolicyMembers;

public class CreatePolicyMembersHandlerTests
{
    private readonly Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>> _mockPolicyMemberRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<ILogger<CreatePolicyMembersHandler>> _mockLogger;
    private readonly CreatePolicyMembersHandler _handler;

    public CreatePolicyMembersHandlerTests()
    {
        _mockPolicyMemberRepository = new Mock<IPaginatedRepository<PolicyMember, PolicyMemberId>>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockUsersService = new Mock<IUsersService>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockLogger = new Mock<ILogger<CreatePolicyMembersHandler>>();

        // Create real PolicyMemberValidationDataService with mocked dependencies
        var mockProductService = new Mock<IProductService>();
        var mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        var tenantId = new TenantId("test-tenant");
        var mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        var mockValidationLogger = new Mock<ILogger<PolicyMemberValidationDataService>>();

        // Setup feature manager mocks
        mockFeatureManager
            .Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(true);

        // Setup product service mocks
        mockProductService
            .Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string> { "test-plan-id" });

        mockProductService
            .Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-package-type");

        // Setup schema provider mocks
        mockSchemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PolicyMemberFieldsSchema([]));

        // Setup legacy policy service mocks for additional methods
        _mockLegacyPolicyService
            .Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string>());

        // Setup additional legacy policy service mocks
        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PolicyDto>());

        var validationDataService = new PolicyMemberValidationDataService(
            _mockLegacyPolicyService.Object,
            mockProductService.Object,
            mockFeatureManager.Object,
            tenantId,
            mockSchemaProvider.Object,
            mockValidationLogger.Object);

        // Create real IndividualMemberValidationSpecification with mocked dependencies
        var mockUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
            It.IsAny<int>(),
            It.IsAny<Func<int, Task<List<ValidationError>>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberValidationSpec = new IndividualMemberValidationSpecification(
            new MemberMustHaveUniqueEmailSpecification(mockUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>()),
            new MemberMustHaveUniqueHKIDSpecification(mockUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>()),
            new MemberMustHaveUniquePassportSpecification(mockUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>()),
            new MemberMustHaveUniqueStaffNumberSpecification(mockUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>()),
            new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>()),
            new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>()),
            new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>()),
            new DependentMustHaveValidPrimaryMemberSpecification(_mockPolicyMemberQueryService.Object, Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>()),
            new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>()),
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        _handler = new CreatePolicyMembersHandler(
            _mockPolicyMemberRepository.Object,
            _mockLegacyPolicyService.Object,
            individualMemberValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockLogger.Object,
            validationDataService);
    }

    private void SetupValidationServiceMocks(List<string> memberIds)
    {
        // Create individuals that match the member IDs
        var individuals = memberIds.Select(id => new Individual { InternalCode = id }).ToList();

        _mockUsersService
            .Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individuals);

        _mockPolicyMemberQueryService
            .Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, PolicyMember?>());

        _mockPolicyMemberQueryService
            .Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, List<PolicyMember>>());
    }

    [Fact]
    public async Task Handle_WithValidInput_ShouldReturnSuccessResponse()
    {
        // Arrange
        var command = new CreatePolicyMembersCommand
        {
            PolicyId = "12345678-1234-1234-1234-123456789012",
            PolicyMembers = new List<PolicyMemberToCreate>
            {
                new()
                {
                    MemberId = "test-member-id",
                    PlanId = "test-plan-id",
                    Fields = new Dictionary<string, object> { ["name"] = "John Doe" }
                }
            },
            SkipMemberValidation = true
        };

        // Use dates that ensure the policy member state is active regardless of timezone
        var startDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-1)); // Start yesterday
        var endDate = DateOnly.FromDateTime(DateTime.Today.AddDays(365)); // End in a year

        var policyDto = new PolicyDto
        {
            Id = "12345678-1234-1234-1234-123456789012",
            IsV2 = false,
            StartDate = startDate,
            EndDate = endDate,
            ProductId = new ProductIdDto { Plan = "test-plan", Type = "test-type", Version = "1.0" },
            ContractHolderId = "test-contract-holder"
        };

        var policyMember = PolicyMember.Create(
            Guid.Parse("12345678-1234-1234-1234-123456789012"),
            "test-member-id",
            startDate,
            endDate,
            "test-plan-id",
            null,
            null,
            new List<PolicyField> { new() { Key = "name", Value = "John Doe" } });

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(command.PolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        // Setup validation service mocks
        SetupValidationServiceMocks(new List<string> { "test-member-id" });

        _mockPolicyMemberRepository
            .Setup(x => x.InsertBatchAsync(It.IsAny<List<PolicyMember>>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMembers.Should().HaveCount(1);

        _mockPolicyMemberRepository.Verify(
            x => x.InsertBatchAsync(It.IsAny<List<PolicyMember>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithPolicyNotFound_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        var command = new CreatePolicyMembersCommand
        {
            PolicyId = "non-existent-policy",
            PolicyMembers = new List<PolicyMemberToCreate>
            {
                new() { MemberId = "test-member-id", PlanId = "test-plan-id" }
            }
        };

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(command.PolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyDto?)null);

        // Act & Assert
        await Assert.ThrowsAsync<PolicyNotFoundException>(() =>
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithMultipleMembers_ShouldProcessAllMembers()
    {
        // Arrange
        var command = new CreatePolicyMembersCommand
        {
            PolicyId = "12345678-1234-1234-1234-123456789012",
            PolicyMembers = new List<PolicyMemberToCreate>
            {
                new() { MemberId = "member-1", PlanId = "plan-1" },
                new() { MemberId = "member-2", PlanId = "plan-2" },
                new() { MemberId = "member-3", PlanId = "plan-3" }
            },
            SkipMemberValidation = true
        };

        // Use dates that ensure the policy member state is active regardless of timezone
        var startDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-1)); // Start yesterday
        var endDate = DateOnly.FromDateTime(DateTime.Today.AddDays(365)); // End in a year

        var policyDto = new PolicyDto
        {
            Id = "12345678-1234-1234-1234-123456789012",
            IsV2 = false,
            StartDate = startDate,
            EndDate = endDate,
            ProductId = new ProductIdDto { Plan = "test-plan", Type = "test-type", Version = "1.0" },
            ContractHolderId = "test-contract-holder"
        };

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(command.PolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        // Setup validation service mocks
        SetupValidationServiceMocks(new List<string> { "member-1", "member-2", "member-3" });

        _mockPolicyMemberRepository
            .Setup(x => x.InsertBatchAsync(It.IsAny<List<PolicyMember>>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyMembers.Should().HaveCount(3);

        _mockPolicyMemberRepository.Verify(
            x => x.InsertBatchAsync(It.IsAny<List<PolicyMember>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithValidationErrors_ShouldThrowValidationException()
    {
        // Arrange
        var command = new CreatePolicyMembersCommand
        {
            PolicyId = "12345678-1234-1234-1234-123456789012",
            PolicyMembers = new List<PolicyMemberToCreate>
            {
                new()
                {
                    MemberId = "test-member-id",
                    PlanId = "test-plan-id",
                    Fields = new Dictionary<string, object> { ["name"] = "John Doe" }
                }
            },
            SkipMemberValidation = false // Enable validation to trigger errors
        };

        var policyDto = new PolicyDto
        {
            Id = "12345678-1234-1234-1234-123456789012",
            IsV2 = false,
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            ProductId = new ProductIdDto { Plan = "test-plan", Type = "test-type", Version = "1.0" },
            ContractHolderId = "test-contract-holder"
        };

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(command.PolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        // Setup validation service mocks to trigger validation error
        // Return empty list of individuals to simulate that the individual doesn't exist
        _mockUsersService
            .Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Individual>()); // Empty list means individual doesn't exist

        _mockPolicyMemberQueryService
            .Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, PolicyMember?>());

        _mockPolicyMemberQueryService
            .Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, List<PolicyMember>>());

        // Mock the validation specification to return errors
        var mockValidationSpec = new Mock<IndividualMemberValidationSpecification>(
            new MemberMustHaveUniqueEmailSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>()),
            new MemberMustHaveUniqueHKIDSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>()),
            new MemberMustHaveUniquePassportSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>()),
            new MemberMustHaveUniqueStaffNumberSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>()),
            new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>()),
            new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>()),
            new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>()),
            new DependentMustHaveValidPrimaryMemberSpecification(Mock.Of<IPolicyMemberQueryService>(), Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>()),
            new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>()),
            Mock.Of<IConcurrentMemberProcessor>(),
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Setup the mock to return validation errors
        mockValidationSpec
            .Setup(x => x.EvaluateBusinessRulesAsync(It.IsAny<IndividualMemberValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BatchValidationResult
            {
                ValidCount = 0,
                InvalidCount = 1,
                RowErrors = new Dictionary<int, List<ValidationError>>
                {
                    { 0, new List<ValidationError> { new ValidationError("TEST_ERROR", "test-member-id", "Test Member", new Dictionary<string, object?>()) } }
                }
            });

        // Create a new handler with the mocked validation specification
        var mockProductService = new Mock<IProductService>();
        var mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        var tenantId = new TenantId("test-tenant");
        var mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        var mockValidationLogger = new Mock<ILogger<PolicyMemberValidationDataService>>();

        // Setup feature manager mocks
        mockFeatureManager
            .Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(true);

        // Setup product service mocks
        mockProductService
            .Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string> { "test-plan-id" });

        mockProductService
            .Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-package-type");

        // Setup schema provider mocks
        mockSchemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PolicyMemberFieldsSchema([]));

        // Setup legacy policy service mocks for additional methods
        _mockLegacyPolicyService
            .Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string>());

        // Setup additional legacy policy service mocks
        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PolicyDto>());

        var validationDataService = new PolicyMemberValidationDataService(
            _mockLegacyPolicyService.Object,
            mockProductService.Object,
            mockFeatureManager.Object,
            tenantId,
            mockSchemaProvider.Object,
            mockValidationLogger.Object);

        var handlerWithMockedValidation = new CreatePolicyMembersHandler(
            _mockPolicyMemberRepository.Object,
            _mockLegacyPolicyService.Object,
            mockValidationSpec.Object,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockLogger.Object,
            validationDataService);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ValidationException>(() =>
            handlerWithMockedValidation.Handle(command, CancellationToken.None));

        exception.Should().NotBeNull();
        exception.Errors.Should().NotBeEmpty();
    }

    [Fact]
    public async Task Handle_WithDomainException_ShouldThrowDirectly()
    {
        // Arrange
        var command = new CreatePolicyMembersCommand
        {
            PolicyId = "12345678-1234-1234-1234-123456789012",
            PolicyMembers = new List<PolicyMemberToCreate>
            {
                new()
                {
                    MemberId = "test-member-id",
                    PlanId = "test-plan-id",
                    Fields = new Dictionary<string, object> { ["name"] = "John Doe" }
                }
            },
            SkipMemberValidation = true
        };

        var policyDto = new PolicyDto
        {
            Id = "12345678-1234-1234-1234-123456789012",
            IsV2 = false,
            StartDate = DateOnly.FromDateTime(DateTime.Today),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            ProductId = new ProductIdDto { Plan = "test-plan", Type = "test-type", Version = "1.0" },
            ContractHolderId = "test-contract-holder"
        };

        _mockLegacyPolicyService
            .Setup(x => x.GetPolicyById(command.PolicyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(policyDto);

        // Setup validation service mocks
        SetupValidationServiceMocks(new List<string> { "test-member-id" });

        // Mock repository to throw a domain exception
        _mockPolicyMemberRepository
            .Setup(x => x.InsertBatchAsync(It.IsAny<List<PolicyMember>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new PolicyMemberExistsException(Guid.NewGuid(), "test-member-id"));

        // Act & Assert
        await Assert.ThrowsAsync<PolicyMemberExistsException>(() =>
            _handler.Handle(command, CancellationToken.None));
    }
}