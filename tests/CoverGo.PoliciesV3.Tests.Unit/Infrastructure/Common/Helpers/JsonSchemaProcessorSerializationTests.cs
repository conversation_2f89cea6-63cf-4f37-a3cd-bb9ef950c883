using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using Newtonsoft.Json.Linq;
using System.Text.Json;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Common.Helpers;

public class JsonSchemaProcessorSerializationTests
{
    [Fact]
    public void ConvertSchemaObjectToJsonElement_WithNewtonsoftJObject_ShouldProduceValidJson()
    {
        // Arrange - Create a JObject that represents a typical schema structure
        var jObject = JObject.Parse("""
        {
            "properties": {
                "firstName": {
                    "type": "string",
                    "meta": {
                        "label": "First Name",
                        "required": true
                    }
                },
                "age": {
                    "type": "number",
                    "meta": {
                        "label": "Age",
                        "required": false
                    }
                }
            }
        }
        """);

        // Act
        JsonElement result = JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(jObject);

        // Assert
        result.ValueKind.Should().Be(JsonValueKind.Object);

        // Verify the structure is preserved correctly
        result.TryGetProperty("properties", out JsonElement properties).Should().BeTrue();
        properties.ValueKind.Should().Be(JsonValueKind.Object);

        // Check firstName field
        properties.TryGetProperty("firstName", out JsonElement firstName).Should().BeTrue();
        firstName.TryGetProperty("type", out JsonElement firstNameType).Should().BeTrue();
        firstNameType.GetString().Should().Be("string");

        firstName.TryGetProperty("meta", out JsonElement firstNameMeta).Should().BeTrue();
        firstNameMeta.TryGetProperty("label", out JsonElement firstNameLabel).Should().BeTrue();
        firstNameLabel.GetString().Should().Be("First Name");

        // Check age field
        properties.TryGetProperty("age", out JsonElement age).Should().BeTrue();
        age.TryGetProperty("type", out JsonElement ageType).Should().BeTrue();
        ageType.GetString().Should().Be("number");

        // Verify no nested empty arrays are present
        string resultJson = result.GetRawText();
        resultJson.Should().NotContain("[[[[");
        resultJson.Should().NotContain("]]]]");
        resultJson.Should().NotContain("[]");
    }

    [Fact]
    public void ConvertSchemaObjectToJsonElement_WithNewtonsoftJProperty_ShouldProduceValidJson()
    {
        // Arrange - Create a JObject containing a property instead of a standalone JProperty
        // because JProperty serialization produces key-value pairs that aren't valid JSON by themselves
        var jObject = new JObject
        {
            ["testField"] = new JObject
            {
                ["type"] = "string",
                ["meta"] = new JObject
                {
                    ["label"] = "Test Field",
                    ["required"] = true
                }
            }
        };

        // Act
        JsonElement result = JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(jObject);

        // Assert
        result.ValueKind.Should().Be(JsonValueKind.Object);

        // Verify the property structure is preserved
        result.TryGetProperty("testField", out JsonElement testField).Should().BeTrue();
        testField.TryGetProperty("type", out JsonElement type).Should().BeTrue();
        type.GetString().Should().Be("string");

        testField.TryGetProperty("meta", out JsonElement meta).Should().BeTrue();
        meta.TryGetProperty("label", out JsonElement label).Should().BeTrue();
        label.GetString().Should().Be("Test Field");

        // Verify no malformed nested arrays
        string resultJson = result.GetRawText();
        resultJson.Should().NotContain("[[[[");
        resultJson.Should().NotContain("]]]]");
    }

    [Fact]
    public void ConvertSchemaObjectToJsonElement_WithRegularObject_ShouldUseSystemTextJson()
    {
        // Arrange - Create a regular .NET object
        var regularObject = new
        {
            properties = new
            {
                testField = new
                {
                    type = "string",
                    meta = new
                    {
                        label = "Test Field",
                        required = true
                    }
                }
            }
        };

        // Act
        JsonElement result = JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(regularObject);

        // Assert
        result.ValueKind.Should().Be(JsonValueKind.Object);

        // Verify the structure is preserved
        result.TryGetProperty("properties", out JsonElement properties).Should().BeTrue();
        properties.TryGetProperty("testField", out JsonElement testField).Should().BeTrue();
        testField.TryGetProperty("type", out JsonElement type).Should().BeTrue();
        type.GetString().Should().Be("string");

        // Verify no malformed nested arrays
        string resultJson = result.GetRawText();
        resultJson.Should().NotContain("[[[[");
        resultJson.Should().NotContain("]]]]");
    }

    [Fact]
    public void ConvertSchemaObjectToJsonElement_WithNull_ShouldReturnEmptyJsonElement()
    {
        // Act
        JsonElement result = JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(null);

        // Assert
        result.Should().Be(JsonSchemaProcessor.EmptyJsonElement);
        result.ValueKind.Should().Be(JsonValueKind.Object);
        result.GetRawText().Should().Be("{}");
    }

    [Fact]
    public void ConvertSchemaObjectToJsonElement_WithComplexNewtonsoftStructure_ShouldNotProduceMalformedArrays()
    {
        // Arrange - Create a complex JObject that might trigger the nested array issue
        var complexJObject = JObject.Parse("""
        {
            "properties": {
                "field1": {
                    "type": "object",
                    "properties": {
                        "nestedField": {
                            "type": "string",
                            "meta": {
                                "label": "Nested Field"
                            }
                        }
                    }
                },
                "field2": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        }
        """);

        // Act
        JsonElement result = JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(complexJObject);

        // Assert
        result.ValueKind.Should().Be(JsonValueKind.Object);

        // Verify the complex structure is preserved
        result.TryGetProperty("properties", out JsonElement properties).Should().BeTrue();
        properties.TryGetProperty("field1", out JsonElement field1).Should().BeTrue();
        field1.TryGetProperty("properties", out JsonElement field1Properties).Should().BeTrue();
        field1Properties.TryGetProperty("nestedField", out JsonElement nestedField).Should().BeTrue();

        // Most importantly, verify no malformed nested empty arrays
        string resultJson = result.GetRawText();
        resultJson.Should().NotContain("[[[[[]],[[[[]],[[]],[[]],[[]]]");
        resultJson.Should().NotContain("[[[[");
        resultJson.Should().NotMatch("\\[\\[\\[\\[.*\\]\\]\\]\\]"); // Regex to catch nested array patterns

        // Verify it's valid JSON by parsing it again
        Action parseAction = () => JsonDocument.Parse(resultJson);
        parseAction.Should().NotThrow();
    }

    [Fact]
    public void ConvertSchemaObjectToJsonElement_WithJToken_ShouldHandleAllJTokenTypes()
    {
        // Arrange - Test different JToken types
        var jArray = JArray.Parse("""["value1", "value2", "value3"]""");
        var jValue = new JValue("test string");
        var jObject = JObject.Parse("""{"key": "value"}""");

        // Act & Assert for JArray
        JsonElement arrayResult = JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(jArray);
        arrayResult.ValueKind.Should().Be(JsonValueKind.Array);
        arrayResult.GetArrayLength().Should().Be(3);

        // Act & Assert for JValue
        JsonElement valueResult = JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(jValue);
        valueResult.ValueKind.Should().Be(JsonValueKind.String);
        valueResult.GetString().Should().Be("test string");

        // Act & Assert for JObject
        JsonElement objectResult = JsonSchemaProcessor.ConvertSchemaObjectToJsonElement(jObject);
        objectResult.ValueKind.Should().Be(JsonValueKind.Object);
        objectResult.TryGetProperty("key", out JsonElement key).Should().BeTrue();
        key.GetString().Should().Be("value");
    }
}
