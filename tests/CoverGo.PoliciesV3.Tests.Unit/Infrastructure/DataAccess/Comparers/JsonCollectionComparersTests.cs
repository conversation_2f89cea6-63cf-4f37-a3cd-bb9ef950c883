using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Comparers;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.DataAccess.Comparers;

public class JsonCollectionComparersTests
{
    #region PolicyFieldCollection Tests

    [Fact]
    public void PolicyFieldCollection_WithIdenticalCollections_ShouldReturnTrue()
    {
        // Arrange
        ICollection<PolicyField> collection1 = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = 42 }
        };
        ICollection<PolicyField> collection2 = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = 42 }
        };

        // Act
        bool result = JsonCollectionComparers.PolicyFieldCollection.Equals(collection1, collection2);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void PolicyFieldCollection_WithDifferentCollections_ShouldReturnFalse()
    {
        // Arrange
        ICollection<PolicyField> collection1 = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" }
        };
        ICollection<PolicyField> collection2 = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value2" }
        };

        // Act
        bool result = JsonCollectionComparers.PolicyFieldCollection.Equals(collection1, collection2);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void PolicyFieldCollection_WithNullCollections_ShouldReturnTrue()
    {
        // Act
        bool result = JsonCollectionComparers.PolicyFieldCollection.Equals(null, null);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void PolicyFieldCollection_WithOneNullCollection_ShouldReturnFalse()
    {
        // Arrange
        ICollection<PolicyField> collection = new List<PolicyField> { new() { Key = "field1", Value = "value1" } };

        // Act
        bool result1 = JsonCollectionComparers.PolicyFieldCollection.Equals(collection, null);
        bool result2 = JsonCollectionComparers.PolicyFieldCollection.Equals(null, collection);

        // Assert
        result1.Should().BeFalse();
        result2.Should().BeFalse();
    }

    [Fact]
    public void PolicyFieldCollection_HashCode_ShouldBeConsistent()
    {
        // Arrange
        ICollection<PolicyField> collection = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = 42 }
        };

        // Act
        int hash1 = JsonCollectionComparers.PolicyFieldCollection.GetHashCode(collection);
        int hash2 = JsonCollectionComparers.PolicyFieldCollection.GetHashCode(collection);

        // Assert
        hash1.Should().Be(hash2);
    }

    [Fact]
    public void PolicyFieldCollection_Snapshot_ShouldCreateDeepCopy()
    {
        // Arrange
        ICollection<PolicyField> original = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = 42 }
        };

        // Act
        ICollection<PolicyField> copy = JsonCollectionComparers.PolicyFieldCollection.Snapshot(original);

        // Assert
        copy.Should().NotBeSameAs(original);
        copy.Should().HaveCount(2);
        var copyList = copy.ToList();
        copyList[0].Key.Should().Be("field1");
        // After JSON serialization/deserialization, the Value becomes a JsonElement
        copyList[0].Value?.ToString().Should().Be("value1");
        copyList[1].Key.Should().Be("field2");
        copyList[1].Value?.ToString().Should().Be("42");
    }

    #endregion

    #region MemberLoadingCollection Tests

    [Fact]
    public void MemberLoadingCollection_WithIdenticalCollections_ShouldReturnTrue()
    {
        // Arrange
        var collection1 = new List<MemberLoading>
        {
            new()
            {
                Id = "loading1",
                Name = "Test Loading",
                Value = 100.50m,
                Method = new("PERCENTAGE"),
                Source = new("MANUAL"),
                Status = new("ACTIVE"),
                EffectiveDate = new DateOnly(2024, 1, 1)
            }
        } as ICollection<MemberLoading>;

        var collection2 = new List<MemberLoading>
        {
            new()
            {
                Id = "loading1",
                Name = "Test Loading",
                Value = 100.50m,
                Method = new("PERCENTAGE"),
                Source = new("MANUAL"),
                Status = new("ACTIVE"),
                EffectiveDate = new DateOnly(2024, 1, 1)
            }
        } as ICollection<MemberLoading>;

        // Act
        bool result = JsonCollectionComparers.MemberLoadingCollection.Equals(collection1, collection2);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void MemberLoadingCollection_WithNullCollections_ShouldReturnTrue()
    {
        // Act
        bool result = JsonCollectionComparers.MemberLoadingCollection.Equals(null, null);

        // Assert
        result.Should().BeTrue();
    }

    #endregion

    #region Generic Create Method Tests

    [Fact]
    public void Create_ShouldReturnValueComparer()
    {
        // Act
        ValueComparer<List<string>> comparer = JsonCollectionComparers.Create<List<string>>();

        // Assert
        comparer.Should().NotBeNull();
        comparer.Should().BeOfType<ValueComparer<List<string>>>();
    }

    [Fact]
    public void Create_WithStringList_ShouldCompareCorrectly()
    {
        // Arrange
        ValueComparer<List<string>> comparer = JsonCollectionComparers.Create<List<string>>();
        var list1 = new List<string> { "item1", "item2" };
        var list2 = new List<string> { "item1", "item2" };
        var list3 = new List<string> { "item1", "item3" };

        // Act & Assert
        comparer.Equals(list1, list2).Should().BeTrue();
        comparer.Equals(list1, list3).Should().BeFalse();
    }

    #endregion
}
