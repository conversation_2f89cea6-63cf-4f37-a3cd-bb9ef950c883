using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Comparers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.DataAccess.Comparers;

/// <summary>
/// Integration tests to verify that JsonCollectionComparers work correctly with Entity Framework change tracking
/// </summary>
public class JsonCollectionComparersIntegrationTests
{
    [Fact]
    public void PolicyFieldCollection_ChangeTracking_ShouldDetectModifications()
    {
        // Arrange
        var comparer = JsonCollectionComparers.PolicyFieldCollection;

        var originalCollection = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = 42 }
        } as ICollection<PolicyField>;

        var modifiedCollection = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = 43 } // Changed value
        } as ICollection<PolicyField>;

        // Act
        bool areEqual = comparer.Equals(originalCollection, modifiedCollection);

        // Assert
        areEqual.Should().BeFalse("because the collections have different values");
    }

    [Fact]
    public void PolicyFieldCollection_ChangeTracking_ShouldDetectAdditions()
    {
        // Arrange
        var comparer = JsonCollectionComparers.PolicyFieldCollection;

        var originalCollection = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" }
        } as ICollection<PolicyField>;

        var modifiedCollection = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = "value2" } // Added field
        } as ICollection<PolicyField>;

        // Act
        bool areEqual = comparer.Equals(originalCollection, modifiedCollection);

        // Assert
        areEqual.Should().BeFalse("because the collections have different number of items");
    }

    [Fact]
    public void PolicyFieldCollection_ChangeTracking_ShouldDetectRemovals()
    {
        // Arrange
        var comparer = JsonCollectionComparers.PolicyFieldCollection;

        var originalCollection = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = "value2" }
        } as ICollection<PolicyField>;

        var modifiedCollection = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" }
            // Removed field2
        } as ICollection<PolicyField>;

        // Act
        bool areEqual = comparer.Equals(originalCollection, modifiedCollection);

        // Assert
        areEqual.Should().BeFalse("because the collections have different number of items");
    }

    [Fact]
    public void PolicyFieldCollection_ChangeTracking_ShouldDetectReordering()
    {
        // Arrange
        var comparer = JsonCollectionComparers.PolicyFieldCollection;

        var originalCollection = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = "value2" }
        } as ICollection<PolicyField>;

        var reorderedCollection = new List<PolicyField>
        {
            new() { Key = "field2", Value = "value2" },
            new() { Key = "field1", Value = "value1" }
        } as ICollection<PolicyField>;

        // Act
        bool areEqual = comparer.Equals(originalCollection, reorderedCollection);

        // Assert
        areEqual.Should().BeFalse("because JSON serialization preserves array order and detects reordering");
    }

    [Fact]
    public void MemberLoadingCollection_ChangeTracking_ShouldDetectModifications()
    {
        // Arrange
        var comparer = JsonCollectionComparers.MemberLoadingCollection;

        var originalCollection = new List<MemberLoading>
        {
            new()
            {
                Id = "loading1",
                Name = "Test Loading",
                Value = 100.50m,
                Method = new("PERCENTAGE"),
                Source = new("MANUAL"),
                Status = new("ACTIVE"),
                EffectiveDate = new DateOnly(2024, 1, 1)
            }
        } as ICollection<MemberLoading>;

        var modifiedCollection = new List<MemberLoading>
        {
            new()
            {
                Id = "loading1",
                Name = "Test Loading",
                Value = 200.50m, // Changed value
                Method = new("PERCENTAGE"),
                Source = new("MANUAL"),
                Status = new("ACTIVE"),
                EffectiveDate = new DateOnly(2024, 1, 1)
            }
        } as ICollection<MemberLoading>;

        // Act
        bool areEqual = comparer.Equals(originalCollection, modifiedCollection);

        // Assert
        areEqual.Should().BeFalse("because the collections have different loading values");
    }

    [Fact]
    public void JsonCollectionComparers_HashCodes_ShouldBeStableAcrossInstances()
    {
        // Arrange
        var collection1 = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = 42 }
        } as ICollection<PolicyField>;

        var collection2 = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" },
            new() { Key = "field2", Value = 42 }
        } as ICollection<PolicyField>;

        // Act
        int hash1 = JsonCollectionComparers.PolicyFieldCollection.GetHashCode(collection1);
        int hash2 = JsonCollectionComparers.PolicyFieldCollection.GetHashCode(collection2);

        // Assert
        hash1.Should().Be(hash2, "because identical collections should have identical hash codes");
    }

    [Fact]
    public void JsonCollectionComparers_Snapshot_ShouldCreateIndependentCopy()
    {
        // Arrange
        var original = new List<PolicyField>
        {
            new() { Key = "field1", Value = "value1" }
        } as ICollection<PolicyField>;

        // Act
        var snapshot = JsonCollectionComparers.PolicyFieldCollection.Snapshot(original);

        // Modify the original
        original.Add(new PolicyField { Key = "field2", Value = "value2" });

        // Assert
        original.Should().HaveCount(2, "because we added an item to the original");
        snapshot.Should().HaveCount(1, "because the snapshot should be independent of the original");
    }
}
