using System.Text.Json;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.ValueObjects;

/// <summary>
/// Comprehensive unit tests for PolicyMemberDataTransformer to verify performance improvements
/// and ensure transformation results remain identical to original implementation.
/// </summary>
public class PolicyMemberDataTransformerTests
{
    #region Test Data Setup

    private static PolicyMemberFieldsSchema CreateTestSchema()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "firstName",
                Label = "First Name",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "lastName",
                Label = "Last Name",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "email",
                Label = "Email Address",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = true
            },
            new()
            {
                Name = "age",
                Label = "Age",
                Type = new NumberFieldType(),
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "gender",
                Label = "Gender",
                Type = new StringFieldType
                {
                    Options = new List<StringOption>
                    {
                        new() { Value = "Male", Label = "Male" },
                        new() { Value = "Female", Label = "Female" },
                        new() { Value = "Other", Label = "Other" }
                    }
                },
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "planType",
                Label = "Plan Type",
                Type = new NumberFieldType
                {
                    Options = new List<NumberOption>
                    {
                        new() { Value = 1, Label = "Basic" },
                        new() { Value = 2, Label = "Premium" },
                        new() { Value = 3, Label = "Enterprise" }
                    }
                },
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "mailingAddress",
                Label = "Mailing Address",
                Type = new ObjectFieldType(new List<PolicyMemberFieldDefinition>
                {
                    new()
                    {
                        Name = "street1",
                        Label = "Street Line 1",
                        Type = new StringFieldType(),
                        IsRequired = true,
                        IsUnique = false
                    },
                    new()
                    {
                        Name = "street2",
                        Label = "Street Line 2",
                        Type = new StringFieldType(),
                        IsRequired = false,
                        IsUnique = false
                    },
                    new()
                    {
                        Name = "city",
                        Label = "City",
                        Type = new StringFieldType(),
                        IsRequired = true,
                        IsUnique = false
                    },
                    new()
                    {
                        Name = "postalCode",
                        Label = "Postal Code",
                        Type = new StringFieldType(),
                        IsRequired = true,
                        IsUnique = false
                    }
                }),
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema(memberFields);
    }

    private static List<IReadOnlyDictionary<string, string?>> CreateTestMemberData(int count = 3)
    {
        var memberData = new List<IReadOnlyDictionary<string, string?>>();

        for (int i = 1; i <= count; i++)
        {
            var member = new Dictionary<string, string?>
            {
                ["First Name"] = $"John{i}",
                ["Last Name"] = $"Doe{i}",
                ["Email Address"] = $"john{i}@example.com",
                ["Age"] = (20 + i).ToString(),
                ["Gender"] = i % 2 == 0 ? "Female" : "Male",
                ["Plan Type"] = (i % 3 + 1).ToString(),
                ["Mailing Address - Street Line 1"] = $"{i}23 Main St",
                ["Mailing Address - Street Line 2"] = i % 2 == 0 ? $"Apt {i}" : null,
                ["Mailing Address - City"] = "New York",
                ["Mailing Address - Postal Code"] = $"1000{i}"
            };
            memberData.Add(member);
        }

        return memberData;
    }

    #endregion

    #region Basic Functionality Tests

    [Fact]
    public void TransformLabelsToFieldNames_WithValidData_ShouldTransformCorrectly()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = CreateTestMemberData(1);

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        result.HasMembers.Should().BeTrue();
        result.IsEmpty.Should().BeFalse();

        var transformedMember = result[0];
        transformedMember.Value["firstName"].Should().Be("John1");
        transformedMember.Value["lastName"].Should().Be("Doe1");
        transformedMember.Value["email"].Should().Be("<EMAIL>");
        transformedMember.Value["age"].Should().Be("21");
        transformedMember.Value["gender"].Should().Be("Male");
        transformedMember.Value["planType"].Should().Be("2"); // i=1: 1%3+1 = 2
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithObjectFields_ShouldSerializeToJson()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = CreateTestMemberData(1);

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        transformedMember.Value.Should().ContainKey("mailingAddress");

        string? addressJson = transformedMember.Value["mailingAddress"];
        addressJson.Should().NotBeNull();

        var addressObject = JsonSerializer.Deserialize<Dictionary<string, string?>>(addressJson!);
        addressObject.Should().NotBeNull();
        addressObject!["street1"].Should().Be("123 Main St");
        addressObject["street2"].Should().BeNull();
        addressObject["city"].Should().Be("New York");
        addressObject["postalCode"].Should().Be("10001");
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithStringOptions_ShouldMapCorrectly()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["First Name"] = "John",
                ["Gender"] = "MALE" // Test case insensitive matching
            }
        };

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        transformedMember.Value["gender"].Should().Be("Male");
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithNumberOptions_ShouldMapCorrectly()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["First Name"] = "John",
                ["Plan Type"] = "2" // Should map to Premium plan
            }
        };

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        transformedMember.Value["planType"].Should().Be("2");
    }

    #endregion

    #region Edge Cases and Error Scenarios

    [Fact]
    public void TransformLabelsToFieldNames_WithEmptyData_ShouldReturnEmpty()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = new List<IReadOnlyDictionary<string, string?>>();

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(0);
        result.IsEmpty.Should().BeTrue();
        result.HasMembers.Should().BeFalse();
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithNullValues_ShouldSkipNullFields()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["First Name"] = "John",
                ["Last Name"] = null,
                ["Email Address"] = "",
                ["Age"] = "25"
            }
        };

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        transformedMember.Value["firstName"].Should().Be("John");
        transformedMember.Value.Should().NotContainKey("lastName");
        transformedMember.Value.Should().NotContainKey("email");
        transformedMember.Value["age"].Should().Be("25");
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithUnknownLabels_ShouldIgnoreUnknownFields()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["First Name"] = "John",
                ["Unknown Field"] = "SomeValue",
                ["Another Unknown"] = "AnotherValue"
            }
        };

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        transformedMember.Value["firstName"].Should().Be("John");
        transformedMember.Value.Should().NotContainKey("Unknown Field");
        transformedMember.Value.Should().NotContainKey("Another Unknown");
        transformedMember.Value.Should().HaveCount(1);
    }

    #endregion

    #region Object Field Serialization Tests

    [Fact]
    public void TransformLabelsToFieldNames_WithPartialObjectFields_ShouldSerializePartialData()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["First Name"] = "John",
                ["Mailing Address - Street Line 1"] = "123 Main St",
                ["Mailing Address - City"] = "New York"
                // Missing Street Line 2 and Postal Code
            }
        };

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        string? addressJson = transformedMember.Value["mailingAddress"];
        addressJson.Should().NotBeNull();

        var addressObject = JsonSerializer.Deserialize<Dictionary<string, string?>>(addressJson!);
        addressObject.Should().NotBeNull();
        addressObject!["street1"].Should().Be("123 Main St");
        addressObject["city"].Should().Be("New York");
        addressObject.Should().NotContainKey("street2");
        addressObject.Should().NotContainKey("postalCode");
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithAllEmptyObjectFields_ShouldReturnNull()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["First Name"] = "John",
                ["Mailing Address - Street Line 1"] = "",
                ["Mailing Address - Street Line 2"] = null,
                ["Mailing Address - City"] = "",
                ["Mailing Address - Postal Code"] = ""
            }
        };

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        transformedMember.Value["mailingAddress"].Should().BeNull();
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithInvalidObjectFieldFormat_ShouldIgnoreInvalidFields()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["First Name"] = "John",
                ["Mailing Address"] = "Invalid format", // Missing separator
                ["Mailing Address - Street Line 1"] = "123 Main St",
                ["Invalid - Format - Too - Many - Parts"] = "Should be ignored"
            }
        };

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        string? addressJson = transformedMember.Value["mailingAddress"];
        addressJson.Should().NotBeNull();

        var addressObject = JsonSerializer.Deserialize<Dictionary<string, string?>>(addressJson!);
        addressObject.Should().NotBeNull();
        addressObject!["street1"].Should().Be("123 Main St");
        addressObject.Should().HaveCount(1); // Only valid field should be included
    }

    #endregion

    #region Performance and Parallel Processing Tests

    [Fact]
    public void TransformLabelsToFieldNames_WithSmallDataset_ShouldUseSequentialProcessing()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = CreateTestMemberData(50); // Below parallel threshold

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);
        stopwatch.Stop();

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(50);
        result.HasMembers.Should().BeTrue();

        // Verify all members are transformed correctly
        for (int i = 0; i < 50; i++)
        {
            var member = result[i];
            member.Value["firstName"].Should().Be($"John{i + 1}");
            member.Value["email"].Should().Be($"john{i + 1}@example.com");
        }

        // Performance should be reasonable for small datasets
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000);
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithLargeDataset_ShouldUseParallelProcessing()
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = CreateTestMemberData(150); // Above parallel threshold (100)

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);
        stopwatch.Stop();

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(150);
        result.HasMembers.Should().BeTrue();

        // Verify order is preserved (AsOrdered() in parallel processing)
        for (int i = 0; i < 10; i++) // Check first 10 for order preservation
        {
            var member = result[i];
            member.Value["firstName"].Should().Be($"John{i + 1}");
            member.Value["email"].Should().Be($"john{i + 1}@example.com");
        }

        // Performance should be reasonable even for large datasets
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000);
    }

    [Theory]
    [InlineData(1)]
    [InlineData(10)]
    [InlineData(50)]
    [InlineData(100)]
    [InlineData(500)]
    public void TransformLabelsToFieldNames_WithVariousDatasetSizes_ShouldProduceConsistentResults(int memberCount)
    {
        // Arrange
        var schema = CreateTestSchema();
        var memberData = CreateTestMemberData(memberCount);

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(memberCount);

        if (memberCount > 0)
        {
            result.HasMembers.Should().BeTrue();
            result.IsEmpty.Should().BeFalse();

            // Verify first and last members for consistency
            var firstMember = result[0];
            firstMember.Value["firstName"].Should().Be("John1");
            firstMember.Value["email"].Should().Be("<EMAIL>");

            if (memberCount > 1)
            {
                var lastMember = result[memberCount - 1];
                lastMember.Value["firstName"].Should().Be($"John{memberCount}");
                lastMember.Value["email"].Should().Be($"john{memberCount}@example.com");
            }
        }
        else
        {
            result.HasMembers.Should().BeFalse();
            result.IsEmpty.Should().BeTrue();
        }
    }

    #endregion

    #region Schema Edge Cases

    [Fact]
    public void TransformLabelsToFieldNames_WithEmptySchema_ShouldReturnEmptyFields()
    {
        // Arrange
        var schema = new PolicyMemberFieldsSchema(new List<PolicyMemberFieldDefinition>());
        var memberData = CreateTestMemberData(1);

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        var transformedMember = result[0];
        transformedMember.Value.Should().BeEmpty();
    }

    [Fact]
    public void TransformLabelsToFieldNames_WithSchemaWithoutObjectFields_ShouldSkipObjectFieldProcessing()
    {
        // Arrange
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "firstName",
                Label = "First Name",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            }
        };
        var schema = new PolicyMemberFieldsSchema(memberFields);

        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["First Name"] = "John",
                ["Address - Street"] = "123 Main St" // Should be ignored
            }
        };

        // Act
        MembersUploadFields result = PolicyMemberDataTransformer.TransformFileLabelsToInternalFieldNames(memberData, schema);

        // Assert
        var transformedMember = result[0];
        transformedMember.Value["firstName"].Should().Be("John");
        transformedMember.Value.Should().NotContainKey("Address - Street");
        transformedMember.Value.Should().HaveCount(1);
    }

    #endregion
}
