using System.Globalization;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes.Extensions;

/// <summary>
/// Unit tests for DateTimeExtensions to ensure proper culture-safe DateTime parsing and validation.
/// </summary>
public class DateTimeExtensionsTests
{
    #region TryParseDateTime Tests

    [Fact]
    public void TryParseDateTime_WithValidIsoDate_ShouldReturnTrue()
    {
        // Arrange
        string value = "2023-12-25";

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void TryParseDateTime_WithValidIsoDateTime_ShouldReturnTrue()
    {
        // Arrange
        string value = "2023-12-25T14:30:00Z";

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void TryParseDateTime_WithValidDDMMYYYYFormat_ShouldReturnTrue()
    {
        // Arrange
        string value = "25/12/2023";

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void TryParseDateTime_WithValidGeneralDateTime_ShouldReturnTrue()
    {
        // Arrange
        string value = "December 25, 2023";

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData("2023-12-25")]
    [InlineData("2023-12-25T14:30:00Z")]
    [InlineData("2023-12-25T14:30:00+05:00")]
    [InlineData("25/12/2023")]
    [InlineData("2023-01-01T00:00:00")]
    public void TryParseDateTime_WithValidFormats_ShouldReturnTrue(string value)
    {
        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void TryParseDateTime_WithInvalidFormat_ShouldReturnFalse()
    {
        // Arrange
        string value = "invalid-date";

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void TryParseDateTime_WithEmptyString_ShouldReturnFalse()
    {
        // Arrange
        string value = "";

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void TryParseDateTime_WithNullValue_ShouldReturnFalse()
    {
        // Arrange
        string? nullValue = null;

        // Act
        var result = DateTimeExtensions.TryParseDateTime(nullValue!);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void TryParseDateTime_WithWhitespaceString_ShouldReturnFalse()
    {
        // Arrange
        string value = "   ";

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("not-a-date")]
    [InlineData("2023-13-25")] // Invalid month
    [InlineData("2023-12-32")] // Invalid day
    [InlineData("25-12-2023")] // Wrong format
    [InlineData("abc123")]
    [InlineData("2023")]
    public void TryParseDateTime_WithInvalidFormats_ShouldReturnFalse(string value)
    {
        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void TryParseDateTime_WithLeapYearDate_ShouldReturnTrue()
    {
        // Arrange
        string value = "2024-02-29"; // Leap year

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void TryParseDateTime_WithNonLeapYearFeb29_ShouldReturnFalse()
    {
        // Arrange
        string value = "2023-02-29"; // Not a leap year

        // Act
        bool result = value.TryParseDateTime();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void TryParseDateTime_UsesInvariantCulture_ShouldBeConsistent()
    {
        // Arrange
        string value = "25/12/2023";
        var originalCulture = CultureInfo.CurrentCulture;

        try
        {
            // Act & Assert - Test with different cultures
            CultureInfo.CurrentCulture = CultureInfo.InvariantCulture;
            bool resultInvariant = value.TryParseDateTime();

            CultureInfo.CurrentCulture = new CultureInfo("en-US");
            bool resultUS = value.TryParseDateTime();

            CultureInfo.CurrentCulture = new CultureInfo("fr-FR");
            bool resultFR = value.TryParseDateTime();

            // All should return the same result due to InvariantCulture usage
            resultInvariant.Should().Be(resultUS);
            resultUS.Should().Be(resultFR);
            resultInvariant.Should().BeTrue(); // Should parse successfully
        }
        finally
        {
            CultureInfo.CurrentCulture = originalCulture;
        }
    }

    #endregion

    #region ParseDateTime Tests

    [Fact]
    public void ParseDateTime_WithValidIsoDate_ShouldReturnCorrectDateTime()
    {
        // Arrange
        string value = "2023-12-25";
        DateTime expected = new(2023, 12, 25, 0, 0, 0, DateTimeKind.Unspecified);

        // Act
        DateTime result = value.ParseDateTime();

        // Assert
        result.Date.Should().Be(expected.Date);
    }

    [Fact]
    public void ParseDateTime_WithValidIsoDateTime_ShouldReturnCorrectDateTime()
    {
        // Arrange
        string value = "2023-12-25T14:30:00";
        DateTime expected = new(2023, 12, 25, 14, 30, 0, DateTimeKind.Unspecified);

        // Act
        DateTime result = value.ParseDateTime();

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void ParseDateTime_WithValidDDMMYYYYFormat_ShouldReturnCorrectDateTime()
    {
        // Arrange
        string value = "25/12/2023";
        DateTime expected = new(2023, 12, 25, 0, 0, 0, DateTimeKind.Unspecified);

        // Act
        DateTime result = value.ParseDateTime();

        // Assert
        result.Date.Should().Be(expected.Date);
    }

    [Theory]
    [InlineData("2023-01-01", 2023, 1, 1)]
    [InlineData("2024-02-29", 2024, 2, 29)] // Leap year
    [InlineData("01/01/2023", 2023, 1, 1)]
    [InlineData("31/12/2023", 2023, 12, 31)]
    public void ParseDateTime_WithValidFormats_ShouldReturnCorrectDateTime(string value, int year, int month, int day)
    {
        // Arrange
        DateTime expected = new(year, month, day, 0, 0, 0, DateTimeKind.Unspecified);

        // Act
        DateTime result = value.ParseDateTime();

        // Assert
        result.Date.Should().Be(expected.Date);
    }

    [Fact]
    public void ParseDateTime_WithNullString_ShouldThrowFormatException()
    {
        // Arrange
        string? value = null;

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => value!.ParseDateTime());
        exception.Message.Should().Be("Cannot parse null or empty string as DateTime");
    }

    [Fact]
    public void ParseDateTime_WithEmptyString_ShouldThrowFormatException()
    {
        // Arrange
        string value = "";

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => value.ParseDateTime());
        exception.Message.Should().Be("Cannot parse null or empty string as DateTime");
    }

    [Fact]
    public void ParseDateTime_WithWhitespaceString_ShouldThrowFormatException()
    {
        // Arrange
        string value = "   ";

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => value.ParseDateTime());
        exception.Message.Should().Be("Cannot parse null or empty string as DateTime");
    }

    [Fact]
    public void ParseDateTime_WithInvalidFormat_ShouldThrowFormatException()
    {
        // Arrange
        string value = "invalid-date";

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => value.ParseDateTime());
        exception.Message.Should().Be("Unable to parse 'invalid-date' as DateTime using supported formats");
    }

    [Theory]
    [InlineData("not-a-date")]
    [InlineData("2023-13-25")] // Invalid month
    [InlineData("2023-12-32")] // Invalid day
    [InlineData("25-12-2023")] // Wrong format
    [InlineData("abc123")]
    [InlineData("2023")]
    public void ParseDateTime_WithInvalidFormats_ShouldThrowFormatException(string value)
    {
        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => value.ParseDateTime());
        exception.Message.Should().Be($"Unable to parse '{value}' as DateTime using supported formats");
    }

    [Fact]
    public void ParseDateTime_WithNonLeapYearFeb29_ShouldThrowFormatException()
    {
        // Arrange
        string value = "2023-02-29"; // Not a leap year

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => value.ParseDateTime());
        exception.Message.Should().Be("Unable to parse '2023-02-29' as DateTime using supported formats");
    }

    [Fact]
    public void ParseDateTime_UsesInvariantCulture_ShouldBeConsistent()
    {
        // Arrange
        string value = "25/12/2023";
        DateTime expected = new(2023, 12, 25, 0, 0, 0, DateTimeKind.Unspecified);
        var originalCulture = CultureInfo.CurrentCulture;

        try
        {
            // Act & Assert - Test with different cultures
            CultureInfo.CurrentCulture = CultureInfo.InvariantCulture;
            DateTime resultInvariant = value.ParseDateTime();

            CultureInfo.CurrentCulture = new CultureInfo("en-US");
            DateTime resultUS = value.ParseDateTime();

            CultureInfo.CurrentCulture = new CultureInfo("fr-FR");
            DateTime resultFR = value.ParseDateTime();

            // All should return the same result due to InvariantCulture usage
            resultInvariant.Date.Should().Be(expected.Date);
            resultUS.Date.Should().Be(expected.Date);
            resultFR.Date.Should().Be(expected.Date);
        }
        finally
        {
            CultureInfo.CurrentCulture = originalCulture;
        }
    }

    [Fact]
    public void ParseDateTime_WithTimezoneInfo_ShouldHandleCorrectly()
    {
        // Arrange
        string value = "2023-12-25T14:30:00Z";

        // Act
        DateTime result = value.ParseDateTime();

        // Assert
        result.Year.Should().Be(2023);
        result.Month.Should().Be(12);
        result.Day.Should().Be(25);
        // Note: UTC time might be converted to local time depending on DateTimeStyles
        // We'll verify the core date parts but be flexible about time conversion
        result.Should().NotBe(default(DateTime));
    }

    [Fact]
    public void ParseDateTime_WithTimezoneOffset_ShouldHandleCorrectly()
    {
        // Arrange
        string value = "2023-12-25T14:30:00+05:00";

        // Act
        DateTime result = value.ParseDateTime();

        // Assert
        result.Year.Should().Be(2023);
        result.Month.Should().Be(12);
        result.Day.Should().Be(25);
        // Note: Time might be adjusted for timezone, so we mainly check date components
    }

    #endregion
}
