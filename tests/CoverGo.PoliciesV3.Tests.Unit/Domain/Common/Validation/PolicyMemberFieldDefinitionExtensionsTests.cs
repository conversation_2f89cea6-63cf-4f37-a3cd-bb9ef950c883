using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Common.Validation;

/// <summary>
/// Unit tests for PolicyMemberFieldDefinitionExtensions to ensure proper validation error creation.
/// </summary>
public class PolicyMemberFieldDefinitionExtensionsTests
{
    private readonly PolicyMemberFieldDefinition _testField;

    public PolicyMemberFieldDefinitionExtensionsTests()
    {
        _testField = new PolicyMemberFieldDefinition
        {
            Name = "email",
            Label = "Email Address",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };
    }

    [Fact]
    public void GetFullLabel_WithLabel_ShouldReturnLabel()
    {
        // Act
        string result = _testField.GetFullLabel();

        // Assert
        result.Should().Be("Email Address");
    }

    [Fact]
    public void GetFullLabel_WithoutLabel_ShouldReturnName()
    {
        // Arrange
        var field = new PolicyMemberFieldDefinition
        {
            Name = "phone",
            Label = "",
            Type = new StringFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        // Act
        string result = field.GetFullLabel();

        // Assert
        result.Should().Be("phone");
    }

    [Fact]
    public void CreateRequiredError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateRequiredError();

        // Assert
        error.Code.Should().Be(ErrorCodes.Required);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Message.Should().Be("Email Address is required");
    }

    [Fact]
    public void CreateInvalidFormatError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateInvalidFormatError();

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidFormat);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Message.Should().Be("Email Address has an invalid format");
    }

    [Fact]
    public void CreateInvalidOptionError_ShouldCreateCorrectError()
    {
        // Arrange
        string[] availableOptions = ["active", "inactive", "pending"];

        // Act
        ValidationError error = _testField.CreateInvalidOptionError(availableOptions);

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidOption);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Context["AvailableOptions"].Should().BeEquivalentTo(availableOptions);
    }

    [Fact]
    public void CreateNotAllowedError_ShouldCreateCorrectError()
    {
        // Act
        ValidationError error = _testField.CreateNotAllowedError();

        // Assert
        error.Code.Should().Be(ErrorCodes.NotAllowed);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
    }

    [Fact]
    public void AllExtensionMethods_WithFieldWithoutLabel_ShouldUseNameAsLabel()
    {
        // Arrange
        var fieldWithoutLabel = new PolicyMemberFieldDefinition
        {
            Name = "testField",
            Label = "",
            Type = new StringFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        // Act
        ValidationError requiredError = fieldWithoutLabel.CreateRequiredError();
        ValidationError formatError = fieldWithoutLabel.CreateInvalidFormatError();

        // Assert
        requiredError.PropertyLabel.Should().Be("testField");
        formatError.PropertyLabel.Should().Be("testField");
    }
}