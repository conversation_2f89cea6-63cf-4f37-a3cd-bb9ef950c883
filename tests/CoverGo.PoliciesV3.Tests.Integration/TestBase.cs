using CoverGo.GraphQL.Client;
using GraphQL;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.SystemTextJson;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace CoverGo.PoliciesV3.Tests.Integration;

public class TestBase : IClassFixture<CustomWebApplicationFactory>, IAsyncLifetime
{
    protected readonly CustomWebApplicationFactory _factory;
    public CoverGoGraphQlClient Gateway { get; private set; } = default!;
    public string TenantId { get; } = UserCredentials.Admin.TenantId;
    readonly Dictionary<string, string> CreatedUsers = new();
    readonly Dictionary<string, string> CreatedLogins = new();
    public GraphQLHttpClient? UserLoggedInClient;

    protected TestBase(CustomWebApplicationFactory factory)
    {
        _factory = factory;
    }

    async Task IAsyncLifetime.InitializeAsync()
    {
        await TestSetup.BuildPoliciesGraphQlClient(null, new(TenantId));
        // Gateway = new CoverGoGraphQlClient();
        // await Gateway.Authorize();
    }

    protected async Task SetLoggedInUser(string user, string? permission = null, string? value = null)
    {

        if (!CreatedUsers.ContainsKey(user))
        {
            string email = $"{Guid.NewGuid()}@covergo.com";
            string loginId = await CreateUsers(email);
            CreatedUsers[user] = email;
            CreatedLogins[user] = loginId;
        }

        if (permission != null)
        {
            await AddTargetedPermission(CreatedLogins[user], permission, value ?? "");
        }

        UserLoggedInClient = await TestSetup.BuildPoliciesGraphQlClient(await TestSetup.GetAccessToken(null, null, CreatedLogins[user], null));
    }

    private async Task<string> CreateUsers(string email)
    {
        string adminToken = await TestSetup.GetAccessToken();
        var authClient = new HttpClient
        {
            BaseAddress = new Uri(TestSetup.Config.Value.AuthUrl)
        };

        authClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", adminToken);

        var requestContent = new
        {
            UserCredentials.Admin.ClientId,
            Username = email,
            Email = email,
            Password = UserCredentials.Admin.Password,
            IsEmailConfirmed = true
        };
        HttpResponseMessage response = await authClient.PostAsync($"{UserCredentials.Admin.TenantId}/api/v1/auth/logins",
            new StringContent(JsonSerializer.Serialize(requestContent),
            System.Text.Encoding.UTF8, "application/json"));

        string content = await response.Content.ReadAsStringAsync();
        var json = JsonNode.Parse(content);
        return json!["value"]!["id"]!.ToString();
    }

    private async Task<GraphQLResponse<DomainUtils.Result>> AddTargetedPermission(string loginId, string permission, string value)
    {
        await TestSetup.GetAccessToken();

        var client = new GraphQLHttpClient(TestSetup.Config.Value.GatewayUrl + "/graphql", new SystemTextJsonSerializer());
        string mutation = $"mutation {{addTargettedPermission(loginId:\"{loginId}\", addTargettedPermissionInput: {{type:\"{permission}\", value:\"{value}\"}}) {{status}}}}";

        return await client.SendMutationAsync<DomainUtils.Result>(new GraphQLRequest(mutation));
    }

    Task IAsyncLifetime.DisposeAsync() =>
        Task.CompletedTask;
}