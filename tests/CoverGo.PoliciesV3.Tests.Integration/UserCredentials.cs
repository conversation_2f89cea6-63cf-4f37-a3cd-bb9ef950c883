namespace CoverGo.PoliciesV3.Tests.Integration;

internal class UserCredentials
{
    public required string TenantId;
    public required string ClientId;
    public required string UserName;
    public required string Password;

    public static UserCredentials Admin => new()
    {
        ClientId = "admin",
        Password = "V9K&KobcZO3",
        UserName = "<EMAIL>",
        TenantId = "covergo"
    };
}
